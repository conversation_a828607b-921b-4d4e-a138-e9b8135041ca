"use client";
import axios from "axios";
import Link from "next/link";
import { notification } from "antd";
import { useMutation } from "@tanstack/react-query";
import { CollectionEnum, DeleteCollections, PostCollections } from "@pcsc/api";
import { isClient } from "@pcsc/utils";
import { useUserStore } from "@pcsc/store-providers";
import { FavoriteListType } from "@/stores/userStore";
import { ErrorCode } from "@pcsc/api";
import { closeToast, showErrorToast, showSuccessToast } from "@pcsc/apps-components";

type collectionProps = {
  isFavorite: boolean;
  collection: collectionType;
  onAuthorizationError?: () => void;
};
type collectionType = {
  itemId?: string;
  type?: keyof typeof CollectionEnum;
  title?: string;
  timestamp?: string;
};

enum keyToName {
  "sale" = "優惠",
  "product" = "商品",
  "news" = "文章",
}

export const onErrorHandling = (error: any, onAuthorizationError: any) => {
  if (axios.isAxiosError(error)) {
    const errorStatus = error.response?.status;
    const errorcode = error.response?.data.code;
    switch (errorStatus) {
      case 401:
        onAuthorizationError && onAuthorizationError();
        break;
      case 403:
        onAuthorizationError && onAuthorizationError();
        break;
      case 409:
        break;
      default:
        showErrorToast({ content: ErrorCode[errorcode] || "服務維護中，請稍候再試一次" });
    }
  }
};

const useCollectionFavorite = ({
  isFavorite,
  collection,
  onAuthorizationError,
}: collectionProps) => {
  const { uniOpen } = useUserStore((state) => state);
  const { updateFavoriteList, favoriteList } = uniOpen;

  const {
    mutateAsync: addCollectionMutate,
    isPending: isAddPending,
    error: addErr,
  } = useMutation({
    mutationFn: () =>
      PostCollections({
        type: CollectionEnum[collection.type as keyof typeof CollectionEnum],
        title: collection.title || "",
        itemId: collection.itemId || "",
        timestamp: collection.timestamp || undefined,
      }),
    onSuccess: () => {
      showSuccessToast({
        content: (
          <div className="flex justify-between s:justify-center">
            <p className="mr-3 whitespace-nowrap">
              你已成功將{keyToName[collection.type as keyof FavoriteListType]}加入收藏
            </p>
            <Link
              className="btn-link whitespace-nowrap px-2"
              href={`/memberCenter/myCollect?collectType=${collection.type}`}
            >
              查看
            </Link>
          </div>
        ),
        duration: 6000,
        id: collection.itemId,
      });

      if (updateFavoriteList) {
        updateFavoriteList(collection.type as keyof FavoriteListType, [
          collection.itemId || "",
          ...(favoriteList[collection.type as keyof FavoriteListType] || []),
        ]);
      }
    },
    onError: (error) => {
      onErrorHandling(error, onAuthorizationError);
    },
  });

  const {
    mutateAsync: deleteCollectionMutate,
    isPending: isDeletePending,
    error: deleteErr,
  } = useMutation({
    mutationFn: () =>
      DeleteCollections({
        type: CollectionEnum[collection.type as keyof FavoriteListType],
        itemId: collection.itemId || "",
      }),
    onSuccess: () => {
      notification.destroy(`success_notification_${collection.itemId}`);
      showSuccessToast({
        content: (
          <div className="flex justify-between s:justify-center">
            <p className="mr-3 whitespace-nowrap">
              你已成功將{keyToName[collection.type as keyof FavoriteListType]}取消收藏
            </p>
            <button
              className="btn-link whitespace-nowrap px-2"
              onClick={() => {
                closeToast(collection.itemId);
                addCollectionMutate();
              }}
            >
              復原
            </button>
          </div>
        ),
        id: collection.itemId,
      });
      if (updateFavoriteList) {
        updateFavoriteList(
          collection.type as keyof FavoriteListType,
          (favoriteList[collection.type as keyof FavoriteListType] || []).filter(
            (item) => item !== collection.itemId,
          ),
        );
      }
    },
    onError: (error) => {
      onErrorHandling(error, onAuthorizationError);
    },
  });

  return {
    mutateCollection: isFavorite ? addCollectionMutate : deleteCollectionMutate,
    isPending: isAddPending || isDeletePending,
    error: addErr || deleteErr,
  };
};

function checkCollectionType(): string | null {
  if (!isClient()) {
    return null;
  }
  const url = window.location.href;

  // Define the mappings
  const mappings: { [key: string]: string } = {
    sales: "sale",
    news: "news",
    horoscope: "news",
    search: "news",
    shopping: "product",
  };

  // Loop through the mappings and check if the URL contains the key
  for (const key in mappings) {
    if (url.includes(key)) {
      return mappings[key];
    }
  }

  // If no match found, return null
  return null;
}

export { useCollectionFavorite, checkCollectionType };
