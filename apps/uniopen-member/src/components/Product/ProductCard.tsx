/* eslint-disable @next/next/no-img-element */
"use client";
import ShareIconSet from "@/components/ShareIconSet";
import { ArrowRight } from "@pcsc/icons/directional";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { type RefObject, useMemo, useEffect, useState } from "react";
import FavoriteIcon, { type CollectionType } from "../FavoriteIcon";
import parse from "html-react-parser";
import { appconfigEnv, sendGTMClick } from "@pcsc/utils";
import { calcDiscount } from "@/utils/dataFormat";
import { Skeleton, TextButton } from "@pcsc/ui-components";
import useQueryBusinessUnitList from "@/hooks/useQueryBusinessUnitList";
import { isValidUrl } from "./utils";
import {
  type ProductStatusType,
  ProductStatusEnum,
  type BusinessUnitType,
  ProductVoucherEnum,
} from "@pcsc/api";
import { platformWithEgList } from "@/constants/platforms";
import { cn } from "@pcsc/tailwind-config/utils";
import DiscountTag from "./DiscountTag";
import { Image } from "@pcsc/ui-components";
import DOMPurify from "isomorphic-dompurify";
import { useIsMounted } from "usehooks-ts";
import type {
  AddToWishlistBuilderType,
  ShareEventBuilderType,
  SelectItemBuilderType,
} from "@pcsc/gtm-event";

type ProductInfoType = {
  id: string;
  categories?: string[];
  dataSource: string;
  name: string;
  productUrl: string;
  internalUrl: string | null;
  price: number;
  salePrice: number;
  promotion?: number;
  button?: { title: string; link: string };
  status: ProductStatusType;
  bu: string;
  tags: string[];
  spec?: { name: string; price: number }[];
};

//FIXME: 暫時解，之後要重構
export type CommonProductCardType = {
  img: string;
  product: ProductInfoType;
};

export type ProductCardProps = CommonProductCardType & {
  className?: string;
  withTag?: boolean;
  hideBu?: boolean;
  contentWithWhiteBg?: boolean;
  updateProductCardData?: (type: string, itemId: string) => void;
  isLoading?: boolean;
  gaParam?: string;
  gtmShareEvent?: ShareEventBuilderType;
  gtmAddToWishlistEvent?: AddToWishlistBuilderType;
  gtmSelectItemEvent?: SelectItemBuilderType;
  ref?: RefObject<HTMLDivElement> | ((node?: Element | null) => void);
  withDiscountTag?: boolean;
  tagControl?: {
    discountTag: { isShow: boolean; text: string };
    rankingTag: { isShow: boolean; text: string };
  };
};

const outOfStockStatus = [ProductStatusEnum.OUT_OF_STOCK, ProductStatusEnum.SOLD_OUT] as const;
// mock: should get favorite id list from backend

export const PointTag: React.FC<{ point: number; className?: string }> = ({ point, className }) => {
  return (
    <div
      className={cn(
        "inline-block w-fit rounded-full rounded-se-xl bg-ct/tag-point/main/container px-2 text-ct/text/inverse/general",
        className,
      )}
    >
      <div className="flex h-6 items-center text-button.sm font-semibold before:-ml-2 before:mr-1 before:inline-block before:h-6 before:w-6 before:rounded-full before:bg-at/gradient/secondary/default before:text-center before:text-[16px]/[24px] before:font-bold before:text-ct/text/inverse/general before:content-['P']">
        {`送 ${point}`}
      </div>
    </div>
  );
};

export const TicketTag = ({ type }: { type: ProductVoucherEnum }) => {
  let colorClassName = "";
  let labelText = "";
  switch (type) {
    case ProductVoucherEnum.PHYSICAL:
      colorClassName = "bg-at/gradient/secondary/light text-ct/text/minor/strong";
      labelText = "實體券";
      break;
    case ProductVoucherEnum.DIGITAL:
      colorClassName = "bg-at/gradient/primary/light text-ct/text/moderate/strong";
      labelText = "電子券";
      break;
  }
  return (
    <div className={cn("w-fit rounded-md px-[6px] py-1 text-button.sm", colorClassName)}>
      {labelText}
    </div>
  );
};

export const ProductCardSkeleton = ({
  contentWithWhiteBg,
  className,
}: {
  contentWithWhiteBg?: boolean;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        "relative flex flex-col rounded-xl",
        contentWithWhiteBg && "h-full shadow-Card_light.Default hover:shadow-Card_light.Hover",
        className,
      )}
    >
      <div className="group relative">
        <div
          className={cn(
            "absolute z-10 aspect-square w-full animate-pulse cursor-pointer rounded-t-xl bg-ct/placeholder-box/strong transition",
            !contentWithWhiteBg && "rounded-b-xl",
          )}
        />
        <div className="aspect-square w-full rounded-t-xl" />
        <div
          className={cn(
            "relative flex-1",
            contentWithWhiteBg
              ? "rounded-b-xl bg-ct/card-basic/main/container px-5 py-3 pb-[16px] pt-[12px]"
              : "py-3",
          )}
        >
          <div className="flex flex-col gap-1.5" data-testid="productCardSkeleton">
            <div className="h-[22px] w-[60px] animate-pulse rounded bg-ct/placeholder-box/general" />
            <div className="h-[22px] w-full animate-pulse rounded bg-ct/placeholder-box/general" />
            <div className="mb-2 h-[22px] w-1/2 animate-pulse rounded bg-ct/placeholder-box/general" />
            <div className="mb-4 h-[22px] w-[60px] animate-pulse rounded bg-ct/placeholder-box/general" />
            <div className="h-[14px] w-20 animate-pulse rounded bg-ct/placeholder-box/general" />
          </div>
        </div>
      </div>
    </div>
  );
};

const BusinessUnitCtaButtonTextMap: Record<string, string> = {
  IOM: platformWithEgList["IOM"],
  "711GO": platformWithEgList["711GO"],
};

const BusinessUnitCtaButton = ({
  currentBusinessUnit,
  product,
}: {
  currentBusinessUnit: BusinessUnitType;
  product: ProductInfoType;
}) => {
  const isMounted = useIsMounted();
  const [buttonText, setButtonText] = useState<string>("");

  useEffect(() => {
    if (!currentBusinessUnit) return;

    const text =
      BusinessUnitCtaButtonTextMap[currentBusinessUnit.code] ??
      `去${platformWithEgList[product.dataSource] || product.dataSource}購買`;

    setButtonText(text);
  }, [currentBusinessUnit, product.dataSource]);

  if (!isMounted()) {
    return null;
  }

  return (
    <TextButton type="moderate" size="m" groupHover>
      <div className="flex items-center gap-1">
        <div className="max-w-[200px] truncate">{buttonText}</div>
        <ArrowRight className="h-4 w-4" />
      </div>
    </TextButton>
  );
};

const ProductCard: React.FC<ProductCardProps> = ({
  img,
  product,
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  withDiscountTag = true,
  hideBu = false,
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  withTag,
  contentWithWhiteBg,
  updateProductCardData,
  isLoading = false,
  gaParam,
  gtmShareEvent,
  gtmAddToWishlistEvent,
  gtmSelectItemEvent,
  tagControl = { discountTag: { isShow: true, text: "" }, rankingTag: { isShow: false, text: "" } },
  className,
  ...rest
}) => {
  const ticketEnabled = appconfigEnv("CONFIG_PUBLIC_TICKET_ENABLED");
  const shoppingDetailEnabled = appconfigEnv("CONFIG_PUBLIC_SHOPPING_DETAIL_ENABLED");

  const router = useRouter();
  const pathName = usePathname();

  const discountTag =
    tagControl.discountTag.isShow && calcDiscount(product.salePrice, product.price);

  const { data: businessUnitData } = useQueryBusinessUnitList();
  const businessUnitList = businessUnitData?.businessUnits;
  const currentBusinessUnit = businessUnitList?.find((item) => item.code === product.dataSource);
  const { urlAlias, officialWebsite, flagshipEnabled, name: buName } = currentBusinessUnit ?? {};

  const isDevTicketMode =
    ticketEnabled && product.categories && product.categories.includes("票券禮券");
  const isDevMultiplePrice = ticketEnabled && product.spec && product.spec.length > 1;

  const sendGaParam = (): void => {
    gaParam && sendGTMClick(gaParam);
    gtmSelectItemEvent && gtmSelectItemEvent.push();
  };

  const handleClickBuName = (): void => {
    if (flagshipEnabled) {
      urlAlias && router.push(`/flagship/${urlAlias}`);
    } else {
      const url = officialWebsite?.startsWith("http")
        ? officialWebsite
        : `https://${officialWebsite}`;
      if (officialWebsite && isValidUrl(url)) {
        window.open(url, "_blank");
      }
    }
  };

  const parsedProductName = useMemo(() => {
    const sanitizedHtml = DOMPurify.sanitize(product.name, {
      USE_PROFILES: { html: true },
    });

    return parse(sanitizedHtml);
  }, [product.name]);

  const collection: CollectionType = {
    type: "product",
    itemId: product.id,
    title: product.name,
  };

  const isOutOfStock = outOfStockStatus.includes(
    product.status as (typeof outOfStockStatus)[number],
  );

  // Link Logic: 若有內部連結 url，則不另開 new tab
  const hasInternalUrl = !!product?.internalUrl && typeof product?.internalUrl === "string";
  const productPageEnabled = shoppingDetailEnabled && hasInternalUrl;
  const linkTarget = productPageEnabled ? "_self" : "_blank";
  const productLink = productPageEnabled ? product?.internalUrl : product?.productUrl;

  return (
    <div
      className={cn(
        "relative flex flex-col rounded-xl",
        contentWithWhiteBg && "h-full shadow-Card_light.Default hover:shadow-Card_light.Hover",
        className,
      )}
      {...rest}
    >
      <div className="group relative">
        {!isLoading && (
          <>
            <Link
              href={productLink}
              target={linkTarget}
              className={cn(
                "absolute z-10 aspect-square w-full cursor-pointer rounded-t-xl transition",
                !contentWithWhiteBg && "rounded-b-xl",
                product.status === ProductStatusEnum.DISCONTINUED
                  ? "bg-ct/placeholder-box/strong"
                  : "group-hover:bg-ct/card-basic/main/dim",
              )}
              onClick={sendGaParam}
              data-testid="productImageClickable"
            />
            <div
              className={cn(
                "absolute right-0 top-0 z-10 opacity-0 group-hover:opacity-100",
                product.status === ProductStatusEnum.DISCONTINUED && "hidden",
              )}
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
              }}
            >
              <ShareIconSet
                path={productLink}
                withBg
                size="s"
                hideIcon={["facebook", "line"]}
                gtmValue={product.name}
                gtmEvent={gtmShareEvent}
              />
            </div>
            <div>
              <FavoriteIcon
                className={cn(
                  "absolute bottom-3 right-3 z-10",
                  product.status === ProductStatusEnum.DISCONTINUED &&
                    !pathName?.includes("myCollect") &&
                    "hidden",
                )}
                data-testid="heartClickable"
                collection={collection}
                updateData={updateProductCardData}
                iconType="inverse-color"
                gtmEvent={gtmAddToWishlistEvent}
              />
            </div>
            <div
              className={cn(
                "pointer-events-none absolute inset-0 z-10 flex items-center justify-center",
                !isOutOfStock && "hidden",
              )}
            >
              <span className="rounded-xl bg-ct/tag-basic/dim/strong px-3.5 py-[18px] text-ct/text/inverse/general">
                缺貨
              </span>
            </div>
            <div
              className={cn(
                "pointer-events-none absolute inset-0 z-10 flex items-center justify-center text-ct/text/inverse/general",
                product.status !== ProductStatusEnum.DISCONTINUED && "hidden",
              )}
            >
              下架
            </div>
          </>
        )}

        {img ? (
          <Image
            src={img}
            aspectRatio="1"
            objectFit="contain"
            className={cn(
              "h-full rounded-t-xl bg-ct/card-basic/main/container",
              !contentWithWhiteBg && "rounded-b-xl",
            )}
            isForcedLoading={isLoading}
            alt={product.name}
          />
        ) : (
          <div
            className={cn(
              "aspect-square w-full rounded-t-xl",
              !contentWithWhiteBg && "rounded-b-xl",
            )}
          />
        )}
      </div>
      {isLoading ? (
        <div className="my-4">
          <Skeleton className="mb-3 h-6 w-full" />
          <Skeleton className="h-6 w-2/3" />
        </div>
      ) : (
        <>
          {/* FIXME: if product-card logic CHECKED, you can delete me */}
          {product.bu === "YAHOO" && product.tags?.includes("挑戰低價") && (
            <DiscountTag type="main">
              挑戰
              <br />
              低價
            </DiscountTag>
          )}
          {!product.tags?.includes("挑戰低價") && discountTag && (
            <DiscountTag type="moderate">{discountTag}</DiscountTag>
          )}
          {tagControl.rankingTag.isShow && (
            <div className="absolute top-0 rounded-br-xl rounded-tl-xl bg-at/gradient/primary/default px-3 py-[6px] text-center text-Subtitle.bold text-ct/text/inverse/general">
              {tagControl.rankingTag.text}
            </div>
          )}
          <div
            className={cn(
              "relative flex-1",
              contentWithWhiteBg
                ? "rounded-b-xl bg-ct/card-basic/main/container px-5 py-3 pb-[16px] pt-[12px]"
                : "py-3",
            )}
          >
            {product.status === ProductStatusEnum.DISCONTINUED ? (
              <div className="flex flex-col gap-1.5" data-testid="discontinuedCard">
                <div className="h-[22px] w-[60px] rounded bg-ct/placeholder-box/general" />
                <div className="h-[22px] w-full rounded bg-ct/placeholder-box/general" />
                <div className="mb-2 h-[22px] w-1/2 rounded bg-ct/placeholder-box/general" />
                <div className="mb-4 h-[22px] w-[60px] rounded bg-ct/placeholder-box/general" />
                <div className="h-[14px] w-20 rounded bg-ct/placeholder-box/general" />
              </div>
            ) : (
              <div className="flex h-full flex-col">
                {!hideBu && buName && (
                  <div className="mb-[6px]">
                    <p
                      className={cn(
                        "relative cursor-pointer text-Body.regular_14 text-ct/text/main/subtlest transition before:absolute before:-top-3 before:h-[calc(100%_+_16px)] before:w-[calc(100%_+_20px)] hover:text-ct/text/moderate/subtlest",
                        contentWithWhiteBg && "before:-left-5",
                      )}
                      onClick={handleClickBuName}
                      data-testid="buName"
                    >
                      {buName}
                    </p>
                  </div>
                )}
                <div className="group flex h-full flex-col justify-between">
                  <Link
                    href={productLink}
                    target={linkTarget}
                    onClick={sendGaParam}
                    data-testid="productLinkClickable"
                  >
                    <div className="mb-3 line-clamp-2 min-h-12 text-Title.regular text-base text-ct/text/main/general group-hover:text-ct/text/moderate/general">
                      {parsedProductName}
                    </div>
                    <div className="mb-3 flex items-center font-poppins">
                      <div className="truncate text-H5.bold">
                        $
                        <span className="px-[2px]">
                          {Intl.NumberFormat("en-US").format(product.salePrice)}
                        </span>
                        {isDevMultiplePrice && <span className="pe-[6px]">起</span>}
                      </div>
                      {product.price > product.salePrice && (
                        <div className="ml-1 flex-1 truncate text-[14px]/[130%] text-ct/text/main/subtlest line-through decoration-ct/text/main/subtlest">
                          ${Intl.NumberFormat("en-US").format(product.price)}
                        </div>
                      )}
                    </div>
                    {product.promotion !== undefined && product.promotion !== 0 && (
                      <PointTag point={product.promotion} className="mb-3" />
                    )}
                    {isDevTicketMode ? (
                      <div className="mb-3 flex gap-[6px]">
                        <TicketTag type={ProductVoucherEnum.PHYSICAL} />
                        <TicketTag type={ProductVoucherEnum.DIGITAL} />
                      </div>
                    ) : null}
                  </Link>
                  {product?.button && (
                    <Link
                      className="flex w-full items-center justify-start gap-1"
                      href={product?.button?.link ?? ""}
                      target={linkTarget}
                    >
                      <BusinessUnitCtaButton
                        currentBusinessUnit={currentBusinessUnit}
                        product={product}
                      />
                    </Link>
                  )}
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default ProductCard;
