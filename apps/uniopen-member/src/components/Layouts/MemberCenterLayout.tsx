"use client";

import Sidebar from "@/app/memberCenter/_components/Sidebar";
import { useUserStore } from "@pcsc/store-providers";
import { useLayoutEffect } from "react";
import { PostLogin, PutLogout } from "@pcsc/api";
import useQueryMemberInformation from "@/hooks/user/useQueryMemberInformation";
import { MemberInfoFieldEnum } from "@pcsc/api";
import { showErrorToast, WebviewConditionWrapper } from "@pcsc/apps-components";
import { sleep } from "radash";

const MemberCenterLayout = ({ children }: { children: React.ReactNode }) => {
  const { uniOpen } = useUserStore((state) => state);
  const { isUser } = uniOpen;

  useLayoutEffect(() => {
    if (isUser === undefined) {
      return;
    }
    if (!isUser) {
      showErrorToast({
        content: (
          <div className="flex gap-3">
            <div className="flex grow justify-between">
              <p>身份已過期</p>
              <button className="text-ct/text/danger/general" onClick={() => PostLogin()}>
                重新登入
              </button>
            </div>
          </div>
        ),
      });

      sleep(6000)
        .then(() => {
          PutLogout("/");
        })
        .catch((err) => {
          console.error("Sleep error", err);
        });
    }
  }, [isUser]);

  useQueryMemberInformation({
    fields: [MemberInfoFieldEnum.horoscope],
  });

  return (
    <WebviewConditionWrapper>
      <div className="flex min-w-[320px] max-w-[1440px] lg:mx-auto">
        <Sidebar />
        <main className="min-h-[calc(100vh_-_124px)] min-w-[calc(100%_-_256px)] max-w-[1184px] flex-grow px-4 pb-20 pt-6 s:px-5 lg:px-8">
          {children}
        </main>
      </div>
    </WebviewConditionWrapper>
  );
};

export default MemberCenterLayout;
