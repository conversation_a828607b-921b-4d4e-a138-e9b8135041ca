"use client";
import React, { useCallback } from "react";
import { createEvent, type EventAttributes, type DateTime, DateArray } from "ics";
import { LinkExternal, Right } from "@pcsc/icons/directional";
import { notify, TextButton, Button } from "@pcsc/ui-components";
import { ReservationPositionEnum, ReservationStatusEnum } from "@pcsc/api";
import { useRouter } from "next/navigation";
import { isAndroid, isFirefox, isIOS, isSafari } from "react-device-detect";
import { env } from "@pcsc/utils";
import { differenceInMinutes } from "date-fns";
export type ReservationButtonType = {
  name: string;
  campaignStartTime: DateTime;
  campaignEndTime: DateTime;
  redeemFullTime: string;
  cancelFullTime: string;
  status: ReservationStatusEnum;
  position: keyof typeof ReservationPositionEnum;
  address: string;
  url: string;
  rules?: string;
  clickRedemptionCode?: () => void;
  clickCancel?: () => void;
};

// 下載 .ics 檔案
async function handleDownload(event: EventAttributes) {
  const filename = "Reservation.ics";
  const file: File = await new Promise((resolve, reject) => {
    createEvent(event, (error, value) => {
      if (error) {
        reject(error);
      }
      resolve(new File([value], filename, { type: "text/calendar" }));
    });
  });
  const url = URL.createObjectURL(file);
  const anchor = document.createElement("a");
  anchor.href = url;
  anchor.download = filename;
  document.body.appendChild(anchor);
  anchor.click();
  document.body.removeChild(anchor);
  URL.revokeObjectURL(url);
}

// Converts HTML to readable plain text. Ideal for .ics files, meta descriptions, or text-only fields.
const htmlTransformPlainText = (html: string): string => {
  const div = document.createElement("div");
  div.innerHTML = html;

  // replace <ol>
  div.querySelectorAll("ol").forEach((ol) => {
    const items = Array.from(ol.querySelectorAll("li"));
    const text = items.map((li, idx) => `${idx + 1}. ${li.textContent?.trim()}`).join("\n");
    ol.replaceWith(document.createTextNode(text));
  });

  // replace <ul>
  div.querySelectorAll("ul").forEach((ul) => {
    const items = Array.from(ul.querySelectorAll("li"));
    const text = items.map((li) => `• ${li.textContent?.trim()}`).join("\n");
    ul.replaceWith(document.createTextNode(text));
  });

  // replace <br>
  div.querySelectorAll("br").forEach((br) => {
    br.replaceWith(document.createTextNode("\n"));
  });

  // replace <p>
  div.querySelectorAll("p").forEach((p) => {
    const text = p.textContent?.trim();
    if (text) {
      p.replaceWith(document.createTextNode(`${text}\n`));
    }
  });

  return div.textContent?.trim() || "";
};

// 加入行事曆的按鈕
const CalendarButton = ({
  name,
  address,
  campaignStartTime,
  campaignEndTime,
  rules,
  url,
}: {
  name: string;
  address: string;
  campaignStartTime: DateTime;
  campaignEndTime: DateTime;
  rules: string;
  url: string;
}) => {
  return (
    <Button
      className="w-full max-w-[360px]"
      onClick={async () => {
        const event = {
          start: campaignStartTime as [number, number, number, number, number],
          end: campaignEndTime as [number, number, number, number, number],
          title: `【${name}預約】`,
          description: htmlTransformPlainText(rules),
          location: address,
          url: url,
        };
        const formatDate = ([y, m, d, h, min]: number[]) =>
          new Date(y, m - 1, d, h, min).toISOString(); // 注意 JS 月份是 0-based

        const params = new URLSearchParams({
          title: event.title,
          description: event.description,
          location: event.location,
          url: event.url,
          start: formatDate(event.start),
          end: formatDate(event.end),
        });
        if (isIOS && !isSafari && !isFirefox) {
          window.open(
            `webcal://${env("NEXT_PUBLIC_SITE_DOMAIN").replace("https://", "")}/api/calendar.ics?${params.toString()}`,
          );
        } else if (
          isAndroid &&
          (navigator.userAgent.toLowerCase().includes("line") ||
            navigator.userAgent.toLowerCase().includes("instagram"))
        ) {
          window.open(
            `intent://${env("NEXT_PUBLIC_SITE_DOMAIN").replace("https://", "")}/api/calendar.ics?${params.toString()}#Intent;scheme=https;package=com.android.chrome;end`,
          );
        } else {
          await handleDownload(event);
        }
      }}
    >
      加入行事曆
    </Button>
  );
};

// 分享連結的按鈕（兩種模式）
const CopyButton = ({ url, type = "filled" }: { url: string; type?: "filled" | "text" }) => {
  const copySuccess = useCallback(async () => {
    notify({
      status: "success",
      content: (
        <div className="flex gap-3">
          <p>成功複製</p>
        </div>
      ),
      duration: 300,
      position: "bottom-center",
    });
    await navigator.clipboard.writeText(url);
  }, [url]);

  if (type === "filled")
    return (
      <Button className="w-full max-w-[360px]" variant="button-stroke" onClick={copySuccess}>
        複製分享連結
      </Button>
    );

  return (
    <div className="flex w-full cursor-pointer justify-center pt-3" onClick={copySuccess}>
      <TextButton size="m" type="moderate">
        <div className="flex items-center gap-1">
          <p>複製分享連結</p>
          <LinkExternal className="size-5" />
        </div>
      </TextButton>
    </div>
  );
};

// 單純以 disabled 方式呈現內容的按鈕
const InfoButton = ({ info, detail }: { info: string; detail?: string }) => {
  return (
    <>
      <Button disabled className="w-full max-w-[360px]">
        {info}
      </Button>
      {detail !== undefined && (
        <p className="w-fit text-Body.medium_12 text-ct/text/main/subtlest">{detail}</p>
      )}
    </>
  );
};

// 到 Member Center 的按鈕
const ForwardToMyReservationButton = () => {
  const router = useRouter();
  return (
    <div
      className="flex w-full cursor-pointer justify-center pt-3"
      onClick={() => {
        router.push("/memberCenter/myReservation?refresh=4000");
      }}
    >
      <TextButton size="m" type="moderate">
        <div className="flex items-center gap-1">
          <p>前往我的預約</p>
          <Right className="size-5" />
        </div>
      </TextButton>
    </div>
  );
};

function isMoreThanOneHourFromNow(dateTime: DateArray): boolean {
  if (!Array.isArray(dateTime) || dateTime.length < 5) return false;

  const [year, month, day, hour, minute] = dateTime;
  const targetDate = new Date(year, month - 1, day, hour, minute);
  const now = new Date();

  return differenceInMinutes(targetDate, now) >= 60;
}

const ButtonGroup = ({
  name,
  status,
  campaignStartTime,
  campaignEndTime,
  redeemFullTime,
  cancelFullTime,
  address,
  url,
  rules,
  position,
  clickRedemptionCode,
  clickCancel,
}: ReservationButtonType) => {
  const enableCancel = isMoreThanOneHourFromNow(campaignStartTime as DateArray);
  switch (status) {
    case ReservationStatusEnum.NOT_REDEEMABLE:
      return (
        <>
          <CalendarButton
            name={name}
            address={address}
            campaignStartTime={campaignStartTime}
            campaignEndTime={campaignEndTime}
            rules={rules}
            url={url}
          />
          {(position === ReservationPositionEnum.SUCCESS ||
            position === ReservationPositionEnum.SHARING) && <CopyButton url={url} />}
          {position === ReservationPositionEnum.MEMBER && (
            <Button
              className="w-full max-w-[360px]"
              variant="button-stroke"
              onClick={clickRedemptionCode}
            >
              輸入核銷碼
            </Button>
          )}
          {position === ReservationPositionEnum.MEMBER && enableCancel && (
            <Button className="w-full max-w-[360px]" variant="button-stroke" onClick={clickCancel}>
              取消預約
            </Button>
          )}

          {position === ReservationPositionEnum.SUCCESS && <ForwardToMyReservationButton />}
          {position === ReservationPositionEnum.MEMBER && <CopyButton url={url} type="text" />}
        </>
      );
    case ReservationStatusEnum.REDEEMABLE:
      return (
        <>
          {(position === ReservationPositionEnum.SUCCESS ||
            position === ReservationPositionEnum.MEMBER) && (
            <Button className="w-full max-w-[360px]" onClick={clickRedemptionCode}>
              輸入核銷碼
            </Button>
          )}
          {(position === ReservationPositionEnum.SUCCESS ||
            position === ReservationPositionEnum.SHARING) && <CopyButton url={url} />}
          {position === ReservationPositionEnum.SUCCESS && <ForwardToMyReservationButton />}
          {position === ReservationPositionEnum.MEMBER && <CopyButton url={url} type="text" />}
        </>
      );
    case ReservationStatusEnum.REDEEMED:
      return <InfoButton info="已成功核銷" detail={`已於 ${redeemFullTime} 核銷`} />;

    case ReservationStatusEnum.EXPIRED:
      return <InfoButton info="已失效" />;
    case ReservationStatusEnum.CANCELED:
      return <InfoButton info="已取消" detail={`已於 ${cancelFullTime} 取消`} />;
    default:
      return null;
  }
};

export default ButtonGroup;
