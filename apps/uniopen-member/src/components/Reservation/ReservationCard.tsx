/* eslint-disable @next/next/no-img-element */
"use client";

import React, { useState, useRef, useMemo } from "react";
import { env } from "@pcsc/utils";
import { type AxiosError } from "axios";
import { DownOutline, Up } from "@pcsc/icons/directional";
import { MapOutlined, PhoneOutlined } from "@pcsc/icons/feature";
import { cn } from "@pcsc/tailwind-config/utils";
import { Dialog, Image, Tag } from "@pcsc/ui-components";
import ButtonGroup from "./ButtonGroup";
import { format } from "date-fns";
import { zhTW } from "date-fns/locale";
import {
  ReservationStatusEnum,
  ReservationEntryMethodEnum,
  type ReservationCardInfo,
  type ReservationPositionEnum,
} from "@pcsc/api";
import { useWindowSize } from "usehooks-ts";
import { formatDateToChinese } from "@pcsc/utils";
import FormInputBox from "@/app/memberCenter/_components/FormInputBox";
import { type SubmitHand<PERSON>, useForm } from "react-hook-form";
import { useRedeemReservation } from "@/hooks/reservation/useRedeemReservation";
import { cancelReservationById } from "@pcsc/api";
import { useMutation } from "@tanstack/react-query";
import { showErrorToast, showSuccessToast } from "@pcsc/apps-components";
import { useRouter } from "next/navigation";
import { htmlTransformList } from "@/utils/htmlTransformList";

export type ReservationCardType = ReservationCardInfo & {
  position: keyof typeof ReservationPositionEnum;
  className?: string;
  defaultOpen?: boolean;
  hasCollapse?: boolean;
  setLoading?: (loading: boolean) => void;
};

function timeFormatter(date: number) {
  const campaignTime = new Date(date);
  return {
    date: format(campaignTime, "yyyy/MM/dd", { locale: zhTW }),
    week: `(${format(campaignTime, "EEEEE", { locale: zhTW })})`,
    time: format(campaignTime, "HH:mm", { locale: zhTW }),
    fullTime: format(campaignTime, "yyyy/MM/dd HH:mm", { locale: zhTW }),
    timeArray: [
      campaignTime.getFullYear(),
      campaignTime.getMonth() + 1, // 這邊一月 = 0，所以要加一
      campaignTime.getDate(),
      campaignTime.getHours(),
      campaignTime.getMinutes(),
    ] as [number, number, number, number, number],
  };
}

type Inputs = {
  redemptionCode: string;
};

const ReservationCard = ({
  reservationInfo: { name, logo, address, rules, notices, phone, reservationEventCode },
  reservationId,
  reservationTime,
  redeemTime,
  cancelTime,
  status,
  reservationNum,
  type,
  position,
  className = "",
  defaultOpen = false,
  hasCollapse = true,
}: ReservationCardType) => {
  const router = useRouter();
  const { width: windowWidth } = useWindowSize();
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const cardRef = useRef<HTMLDivElement>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);

  // 產生狀態標籤
  const statusTag = useMemo(() => {
    switch (status) {
      case ReservationStatusEnum.NOT_REDEEMABLE:
        return (
          <Tag size="S" type="main_subtle">
            尚未開始
          </Tag>
        );
      case ReservationStatusEnum.REDEEMABLE:
        return (
          <Tag size="S" type="moderate_subtle">
            開放進場
          </Tag>
        );
      case ReservationStatusEnum.REDEEMED:
        return (
          <Tag size="S" type="dim_subtle">
            已核銷
          </Tag>
        );
      case ReservationStatusEnum.EXPIRED:
        return (
          <Tag size="S" type="dim_subtle">
            已失效
          </Tag>
        );
      case ReservationStatusEnum.CANCELED:
        return (
          <Tag size="S" type="dim_subtle">
            已取消
          </Tag>
        );
      default:
        return null;
    }
  }, [status]);

  const {
    watch,
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setError,
  } = useForm<Inputs>();

  const { mutate: cancelReservationMutate } = useMutation({
    mutationFn: (id: string) => {
      return cancelReservationById(id);
    },
    onSuccess() {
      setIsCancelDialogOpen(false);
      router.replace("/memberCenter/myReservation?status=cancel&refresh=6000");
      showSuccessToast({ content: `已取消預約${name}!` });
    },
    onError(error: AxiosError) {
      setIsCancelDialogOpen(false);
      const errorData: { code: number } = error.response.data as {
        code: number;
      };
      // Handle request errors
      switch (errorData.code) {
        case 403115:
          showErrorToast({ content: "預約已超過可取消時間，無法取消" });
          router.replace("/memberCenter/myReservation?status=coming&refresh=1");
          break;
        case 403118:
          showErrorToast({ content: "預約已取消，無法再次取消" });
          router.replace("/memberCenter/myReservation?status=coming&refresh=1");
          break;
        case 403119:
          showErrorToast({ content: "預約已核銷，無法取消此預約" });
          router.replace("/memberCenter/myReservation?status=used&refresh=1");
          break;
        case 404201:
        case 409202:
        case 409203:
          showErrorToast({ content: "系統忙碌中，請稍後再試一次" });
          break;
        default:
          showErrorToast({ content: "取消預約失敗" });
          break;
      }
    },
  });

  const { redeemReservationMutate } = useRedeemReservation({
    setError,
    reset,
    reservationName: name,
    onSuccessCallback: () => {
      setIsDialogOpen(false);
      router.replace("/memberCenter/myReservation?status=used&refresh=6000");
    },
  });

  const onSubmit: SubmitHandler<Inputs> = (data) => {
    redeemReservationMutate({
      id: reservationId,
      redeemCode: data.redemptionCode,
      reservationName: name,
    });
  };

  const {
    date: campaignDate,
    week,
    time: startTime,
    timeArray: startTimeArray,
  } = timeFormatter(reservationTime);
  const { timeArray: endTimeArray } = timeFormatter(reservationTime + 60 * 60 * 1000);
  const { fullTime: redeemFullTime } = timeFormatter(redeemTime);
  const { fullTime: cancelFullTime } = timeFormatter(cancelTime);

  const noticePurify = useMemo(() => {
    if (notices) {
      return htmlTransformList(notices);
    }
  }, [notices]);

  return (
    <>
      <div
        className={cn(
          "overflow-hidden rounded-xl shadow-card transition-all duration-300 ease-in-out",
          className,
        )}
        style={{
          height: isOpen ? (hasCollapse ? `${cardRef.current?.scrollHeight}px` : "100%") : "146px",
        }}
      >
        <div
          className={cn(
            "flex w-full flex-col rounded-xl bg-ct/surface/moderate p-4",
            !hasCollapse && "h-full",
          )}
          ref={cardRef}
        >
          <div
            className="flex cursor-pointer items-center justify-between pb-3"
            onClick={() => {
              if (hasCollapse) setIsOpen((prev) => !prev);
            }}
          >
            <p className="text-Title.regular text-ct/text/main/subtle">{name}</p>
            {hasCollapse && (
              <button
                type="button"
                className="flex size-8 items-center justify-center rounded-full hover:bg-ct/button-icon/spotlight/hover [&_svg]:!fill-ct/icon/moderate/general"
              >
                {isOpen ? (
                  <Up className="size-6" />
                ) : (
                  <DownOutline className="size-6 fill-[#714EFF]" />
                )}
              </button>
            )}
          </div>
          <hr className="bg-ct/divider/main/general" />
          <div className="flex items-start gap-2 py-3">
            <Image src={logo} className="size-[60px] rounded-full" alt={name} />
            <div className="flex flex-1 flex-col gap-0.5">
              <div className="text-Title.medium text-ct/text/main/general">
                <span>{campaignDate}</span>
                <span className="ml-1 text-Subtitle.medium">{week}</span>
              </div>
              <h4 className="text-H3.bold text-ct/text/main/general">{startTime}</h4>
            </div>
            {statusTag}
          </div>
          <hr className="bg-ct/divider/main/general" />
          <div className="Body.regular_16 flex gap-2 py-3">
            <div className="flex flex-col gap-2 text-ct/text/main/subtlest">
              <p>入場人數</p>
              <p>入場方式</p>
            </div>
            <div className="flex flex-col gap-2 text-ct/text/main/general">
              <p>{reservationNum} 人</p>
              <p>{type === ReservationEntryMethodEnum.OPENPOINT ? "點數兌換" : "VIP"}</p>
            </div>
          </div>
          <div className={cn("flex flex-grow flex-col items-center gap-3 px-5 pb-6 pt-1")}>
            <ButtonGroup
              name={name}
              campaignStartTime={startTimeArray}
              campaignEndTime={endTimeArray}
              redeemFullTime={redeemFullTime}
              cancelFullTime={cancelFullTime}
              status={status}
              address={address}
              url={`${env("NEXT_PUBLIC_SITE_DOMAIN")}/reservation/${reservationEventCode}/${reservationId}`}
              rules={notices}
              position={position}
              clickRedemptionCode={() => {
                reset(); // reset form state
                setIsDialogOpen(true);
              }}
              clickCancel={() => {
                setIsCancelDialogOpen(true);
              }}
            />
            {/* 核銷 */}
            <Dialog
              title="請輸入核銷碼"
              subTitle="提醒你，核銷後不可回復，請由現場工作人員操作"
              isOpen={isDialogOpen}
              setIsOpen={setIsDialogOpen}
              size={windowWidth < 481 ? "s" : "m"}
              rightButton={{
                children: "確認核銷",
                onClick: handleSubmit(onSubmit),
              }}
            >
              <div className="flex flex-col items-center justify-center gap-3 rounded-xl bg-ct/surface/moderate py-2 text-H4.medium">
                <Image src={logo} className="size-8 rounded-full" alt={name} />
                <div className="flex flex-col items-center gap-2">
                  <p className="font-poppins">
                    {formatDateToChinese(reservationTime, "yyyy/MM/dd")}
                    <span className="pl-1">{formatDateToChinese(reservationTime, "(EEEEE)")}</span>
                  </p>
                  <p className="text-H2.bold">{formatDateToChinese(reservationTime, "HH:mm")}</p>
                  <p>
                    <span>{reservationNum} 人</span>
                  </p>
                </div>
              </div>
              <form>
                <FormInputBox
                  id="redemptionCode"
                  required
                  errorMsg={errors.redemptionCode?.message}
                  placeholder="請輸入核銷碼"
                  value={watch("redemptionCode")}
                  {...register("redemptionCode", {
                    required: "請輸入核銷碼",
                  })}
                />
              </form>
            </Dialog>
            {/* 取消 */}
            <Dialog
              title="確定要取消預約？"
              subTitle="取消即放棄入場資格，若是透過點數兌換完成預約，點數將全數退回"
              isOpen={isCancelDialogOpen}
              setIsOpen={setIsCancelDialogOpen}
              size={windowWidth < 481 ? "s" : "m"}
              leftButton={{
                children: "取消預約",
                onClick: () => {
                  cancelReservationMutate(reservationId);
                },
              }}
              rightButton={{
                children: "保留預約",
                onClick: () => {
                  setIsCancelDialogOpen(false);
                },
              }}
            />
          </div>
          <div className="flex w-full flex-col gap-2 rounded-xl bg-ct/surface/main px-4 py-3">
            {(status === ReservationStatusEnum.NOT_REDEEMABLE ||
              status === ReservationStatusEnum.REDEEMABLE) && (
              <div className="whitespace-break-spaces text-Body.regular_14 text-ct/text/main/subtle">
                {noticePurify}
              </div>
            )}
            <div className="flex items-start gap-3">
              <div className="w-5">
                <MapOutlined className="size-5" />
              </div>
              <p className="break-words text-Body.regular_14 text-ct/text/main/subtle">{address}</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-5">
                <PhoneOutlined className="size-5 fill-ct/icon/main/subtlest" />
              </div>
              <a
                href={`tel:${phone}`}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                className="break-words text-Body.regular_14 text-ct/text/main/subtle no-underline"
              >
                {phone}
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ReservationCard;
