"use client";

import React, { useState } from "react";
import { Tooltip } from "antd";
import { Heart, HeartFilled } from "@pcsc/icons/feature";
import { convertToISO } from "@/utils/dataFormat";
import { useCollectionFavorite } from "@/utils/useCollectionFavorite";
import { Dialog } from "@pcsc/ui-components";
import { useUserStore } from "@pcsc/store-providers";
import { sendGTMClick } from "@pcsc/utils";
import { cn } from "@pcsc/tailwind-config/utils";
import { useWindowSize } from "usehooks-ts";
import type { AddToWishlistBuilderType } from "@pcsc/gtm-event";
import { PostLogin } from "@pcsc/api";

export type CollectionType = {
  type: "sale" | "product" | "news";
  title: string;
  itemId: string;
  timestamp?: number;
};

type FavoriteIconProps = {
  iconSize?: string;
  favorite?: boolean;
  handleToggleFav?: React.MouseEventHandler<HTMLButtonElement>;
  inverse?: boolean;
  clickingArea?: string;
  className?: string;
  collection?: CollectionType;
  updateData?: (data: string, id: string) => void;
  gtmValue?: string;
  gtmEvent?: AddToWishlistBuilderType;
  iconType?: "inverse-neutral" | "inverse-color" | "dim-neutral";
};
const iconStyles = {
  "inverse-neutral":
    "[&>path:first-child]:fill-ct/icon/main/pale [&>path:last-child]:fill-ct/icon/inverse/subtle",
  "inverse-color":
    "[&>path:first-child]:fill-ct/icon/main/pale [&>path:last-child]:fill-ct/icon/inverse/subtle",
  "dim-neutral":
    "[&>path:last-child]:fill-ct/icon/main/subtlest [&>path:first-child]:fill-transparent",
};
const iconEffectStyle = {
  "inverse-neutral":
    "group-hover/fav:[&>path]:fill-ct/icon/inverse/subtlest group-active/fav:[&>path]:fill-ct/icon/inverse/subtlest",
  "inverse-color":
    "group-hover/fav:[&>path]:fill-ct/icon/minor/subtlest group-active/fav:[&>path]:fill-ct/icon/minor/subtlest",
  "dim-neutral":
    "group-hover/fav:[&>path]:fill-ct/icon/minor/subtlest group-active/fav:[&>path]:fill-ct/icon/minor/subtlest",
};
const iconSelectedStyle = {
  "inverse-neutral": "[&_path]:fill-ct/text/inverse/general",
  "inverse-color": "[&_path]:fill-ct/icon/minor/general",
  "dim-neutral": "[&_path]:fill-ct/icon/minor/general",
};

const FavoriteIcon = ({
  iconSize = "w-6 h-6",
  clickingArea = "relative before:h-[40px] before:w-[40px] before:absolute before:-left-2 before:-top-2 ",
  className = "",
  collection,
  updateData,
  gtmValue = "",
  iconType = "inverse-neutral",
  gtmEvent,
}: FavoriteIconProps) => {
  const { width } = useWindowSize();
  const {
    uniOpen: { isUser, favoriteList },
  } = useUserStore((state) => state);

  const isFavorite = Boolean(
    collection && favoriteList[collection?.type]?.includes(collection?.itemId),
  );

  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [dialogTitle, setDialogTitle] = useState<string>("");
  const confirmLogin = (title?: string) => {
    setIsDialogOpen(true);
    setDialogTitle(title ?? "需登入後才能收藏，現在登入！");
  };

  const { mutateCollection, isPending } = useCollectionFavorite({
    isFavorite: !isFavorite,
    collection: {
      type: collection?.type,
      title: collection?.title,
      itemId: collection?.itemId,
      timestamp: collection?.type === "sale" ? convertToISO(collection?.timestamp || 0) : undefined,
    },
    onAuthorizationError: () => {
      confirmLogin("身份已過期，請重新登入");
    },
  });

  const handleToggleFavorite: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    e.preventDefault();
    if (!collection || isPending) return;
    if (!isFavorite) {
      gtmValue && sendGTMClick(`save to fav/${gtmValue}`);
      gtmEvent && gtmEvent?.push();
    }

    mutateCollection()
      .then(() => {
        updateData?.(collection.type, collection.itemId);
      })
      .catch((err: any) => {
        console.error(err);
      });
  };

  const isMobile =
    typeof window === "undefined"
      ? false
      : /mobile|android|iphone|ipad|phone/i.test(navigator.userAgent.toLowerCase());

  return (
    <>
      <button
        onClick={
          isUser
            ? handleToggleFavorite
            : (e) => {
                e.stopPropagation();
                e.preventDefault();
                confirmLogin();
              }
        }
        className={cn("group/fav hover:cursor-pointer", className)}
        data-testid="heartClickable"
        type="button"
      >
        <Tooltip
          title={isMobile ? "" : "可至我的收藏檢視"}
          color="#FFFFFF"
          styles={{ body: { color: "rgba(0,0,0,0.6)" } }}
        >
          <div className={clickingArea}>
            {isFavorite ? (
              <HeartFilled
                className={cn(iconSelectedStyle[iconType], iconSize)}
                data-testid="filledHeartIcon"
              />
            ) : (
              <Heart
                className={cn(iconSize, iconStyles[iconType], iconEffectStyle[iconType])}
                data-testid="normalHeartIcon"
              />
            )}
          </div>
        </Tooltip>
      </button>
      <Dialog
        title={dialogTitle}
        isOpen={isDialogOpen}
        setIsOpen={setIsDialogOpen}
        size={width > 480 ? "m" : "s"}
        rightButton={{
          children: "登入",
          onClick: () => {
            PostLogin();
            setIsDialogOpen(false);
          },
        }}
      />
    </>
  );
};

export default FavoriteIcon;
