import { cn } from "@pcsc/tailwind-config/utils";
import { Tag, Image, type ImageProps } from "@pcsc/ui-components";
import ShareIconSet from "../../ShareIconSet";
import CardFrame from "./CardFrame";
import { type ShareEventBuilderType } from "@pcsc/gtm-event";

const CardBasic = ({
  image,
  imageProps = {},
  isLoading = false,
  specialTag,
  share,
  className = "",
  children,
}: {
  image: string;
  imageProps?: ImageProps;
  isLoading?: boolean;
  specialTag?: string;
  share?: { title?: string; link?: string; gtmEvent?: ShareEventBuilderType } | null | undefined;
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <CardFrame className={cn("flex flex-col", className)}>
      {specialTag && (
        <Tag
          type="moderate_general"
          className="absolute top-3 z-10 max-w-[150px] truncate rounded-l-none"
          size="M"
        >
          {specialTag}
        </Tag>
      )}
      <div className="group relative aspect-video cursor-pointer overflow-hidden">
        <>
          {Boolean(share) && (
            <div
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
              }}
              className="absolute right-0 top-0 z-10 opacity-0 group-hover:opacity-100 [&+.image-mask]:hover:after:bg-ct/card-basic/main/dim"
            >
              <ShareIconSet
                path={share?.link}
                withBg
                gtmValue={share?.title}
                gtmEvent={share?.gtmEvent}
              />
            </div>
          )}
          <Image
            src={image}
            objectFit="cover"
            className={cn(
              "image-mask",
              "aspect-video h-full min-w-full rounded-t-xl",
              "after:absolute after:top-0 after:h-full after:w-full after:rounded-t-xl hover:after:bg-ct/card-basic/main/dim",
            )}
            alt="card_image"
            isForcedLoading={isLoading}
            {...imageProps}
          />
        </>
      </div>
      <div className="relative flex flex-grow rounded-b-xl bg-ct/card-basic/main/container px-5 pb-4 pt-3">
        {children}
      </div>
    </CardFrame>
  );
};

export default CardBasic;
