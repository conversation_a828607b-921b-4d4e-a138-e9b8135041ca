import { PutLogout } from "@pcsc/api";
import { redeemReservationById } from "@pcsc/api";
import { showErrorToast, showSuccessToast } from "@pcsc/apps-components";
import { useMutation } from "@tanstack/react-query";
import { type AxiosError } from "axios";
import { useRouter } from "next/navigation";

type RedeemReservationParams = {
  id: string;
  redeemCode: string;
  reservationName: string;
};

type UseRedeemReservationOptions = {
  setError: (field: string, error: { type: string; message: string }) => void;
  reset: () => void;
  reservationName: string;
  onSuccessCallback?: () => void;
  onErrorCallback?: () => void;
};

export const useRedeemReservation = ({
  setError,
  reset,
  reservationName,
  onSuccessCallback,
  onErrorCallback,
}: UseRedeemReservationOptions) => {
  const router = useRouter();
  const { mutate: redeemReservationMutate, isPending } = useMutation({
    mutationFn: ({ id, redeemCode }: RedeemReservationParams) =>
      redeemReservationById(id, redeemCode),
    onSuccess() {
      onSuccessCallback?.();
      showSuccessToast({ content: `已成功核銷${reservationName}!` });
      reset();
    },
    onError(error: AxiosError) {
      const errorData: { code: number } = error.response?.data as {
        code: number;
      };

      switch (errorData?.code) {
        case 400112:
          setError("redemptionCode", {
            type: "server",
            message: "核銷碼錯誤，請重新輸入",
          });
          break;
        case 403123:
          setError("redemptionCode", {
            type: "server",
            message: "活動尚未開始",
          });
          break;
        case 403121:
          showErrorToast({ content: "預約已核銷，無法再次核銷" });
          router.replace("/memberCenter/myReservation?status=used&refresh=1");
          break;
        case 403122:
          showErrorToast({ content: "預約已取消，無法進行核銷" });
          router.replace("/memberCenter/myReservation?status=cancel&refresh=1");
          break;
        case 403124:
          showErrorToast({ content: "預約已過期，無法進行核銷" });
          router.replace("/memberCenter/myReservation?status=used&refresh=1");
          break;
        case 403113:
        case 403114:
        case 401007:
          showErrorToast({ content: "身份驗證錯誤，請重新登入再操作" });
          PutLogout("/");
          break;
        default:
          showErrorToast({ content: "系統忙碌中，請稍後再試" });
          break;
      }

      onErrorCallback?.();
    },
  });

  return {
    redeemReservationMutate,
    isPending,
  };
};
