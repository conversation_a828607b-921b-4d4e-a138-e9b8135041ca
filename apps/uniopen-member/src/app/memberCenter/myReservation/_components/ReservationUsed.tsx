import ReservationStatus from "./ReservationStatus";
import { Loading } from "./ReservationComing";
import { useQueryReservationListByStatus } from "../hook/useReservationByStatus";
import { ReservationStatusEnum } from "@pcsc/api";

const TabToReservationStatus = {
  used: [ReservationStatusEnum.REDEEMED, ReservationStatusEnum.EXPIRED],
  cancel: [ReservationStatusEnum.CANCELED],
};
const ReservationUsed = ({ status }: { status: keyof typeof TabToReservationStatus }) => {
  const { data: reservationList, isLoading } = useQueryReservationListByStatus(
    TabToReservationStatus[status],
  );

  if (isLoading) {
    return <Loading />;
  }

  return (
    <>
      <p className="mb-6 text-ct/text/main/subtle">顯示最近90天預約紀錄</p>
      <ReservationStatus reservationCardList={reservationList} />
    </>
  );
};

export default ReservationUsed;
