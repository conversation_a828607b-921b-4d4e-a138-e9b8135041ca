"use client";

import { useEffect, useState } from "react";
import { GetMemberInformation, IdBinding, MemberInfoFieldEnum } from "@pcsc/api";
import IdBindingCard from "./_components/IdBindingCard";

const PageContainer = () => {
  const [IdBinding, setIdBinding] = useState<IdBinding | null>(null);
  const [resultArray, setResultArray] = useState<{ name: string; id: string | null }[]>([
    { name: "book", id: null },
    { name: "ppc", id: null },
    { name: "duskin", id: null },
    { name: "organicshops", id: null },
  ]);

  const getIdBinding = async () => {
    const res = await GetMemberInformation({ fields: [MemberInfoFieldEnum.idBinding] });
    if (res && res.idBinding) {
      setIdBinding(res.idBinding);
      let resultArray = [];
      for (let key in res.idBinding) {
        resultArray.push({
          name: key.replace("Id", ""),
          id: res.idBinding[key as keyof IdBinding],
        });
        setResultArray(resultArray);
      }
    }
  };

  useEffect(() => {
    if (!IdBinding) {
      getIdBinding();
    }
  }, [IdBinding]);

  return (
    <div className="mt-10 flex max-w-[1020px] flex-wrap gap-5">
      {resultArray.map((item) => (
        <IdBindingCard key={item.name} name={item.name} id={item.id} />
      ))}
    </div>
  );
};

export default PageContainer;
