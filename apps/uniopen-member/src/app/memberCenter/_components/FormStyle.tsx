import { CheckCircleFilled, WarningFilled } from "@pcsc/icons/feature";
import { LoadingOutlined } from "@ant-design/icons";

interface FieldData {
  name: string[];
  value?: any;
  touched?: boolean;
  validating?: boolean;
  errors?: string[];
}

export const FormItemStyle: React.CSSProperties = {
  marginBottom: 0,
};

export const InputStyle: React.CSSProperties = {
  marginBottom: "8px",
};

export const UniRadioStyle = `
  [&_.ant-radio-wrapper]:text-Title.regular
  [&_.ant-radio-wrapper]:min-h-14
  [&_.ant-radio-wrapper]:p-4
  [&_.ant-radio-wrapper]:mr-0
  [&_.ant-radio-wrapper]:rounded-xl
  [&_.ant-radio-wrapper]:bg-ct/checkbox/main/container/default
  [&_.ant-radio-wrapper]:border-ct/option/stroke/default
  [&_.ant-radio-wrapper]:items-center
  [&_.ant-radio-wrapper>*:nth-child(2)]:w-full
  [&_.ant-radio-wrapper>*:nth-child(2)]:pl-3
  [&_.ant-radio-wrapper>*:nth-child(2)]:pr-0
  [&_.ant-radio-wrapper.ant-radio-wrapper-checked]:bg-ct/option/container/selected
  [&_.ant-radio-wrapper.ant-radio-wrapper-checked]:text-ct/text/inverse/general
  [&_.ant-radio-wrapper.ant-radio-wrapper-checked]:text-Title.medium
  [&_.ant-radio-wrapper:not(.ant-radio-wrapper-checked):hover]:text-ct/text/moderate/general
  [&_.ant-radio-wrapper:not(&_.ant-radio-wrapper-checked):hover_.ant-radio-inner]:border-ct/radio-button/main/focus-outline/hover
  [&_.ant-radio-wrapper:not(.ant-radio-wrapper-disabled):hover_.ant-radio-checked:not(.ant-radio-disabled)_.ant-radio-inner]:bg-ct/option/container/default
  [&_.ant-radio-wrapper:not(.ant-radio-wrapper-disabled):hover_.ant-radio-checked:not(.ant-radio-disabled)_.ant-radio-inner]:border-ct/option/container/selected
  [&_.ant-radio-wrapper:not(.ant-radio-wrapper-disabled):hover_.ant-radio-inner]:border-ct/radio-button/main/focus-outline/default
  [&_.ant-radio-wrapper_.ant-radio-checked::after]:border-ct/radio-button/moderate/unit/selected
  [&_.ant-radio-wrapper_.ant-radio-checked_.ant-radio-inner]:bg-ct/option/container/default
  [&_.ant-radio-wrapper_.ant-radio-checked_.ant-radio-inner]:border-ct/radio-button/main/focus-outline/default
  [&_.ant-radio-wrapper_.ant-radio-checked_.ant-radio-inner::after]:bg-ct/option/container/selected
  [&_.ant-radio-wrapper_.ant-radio-checked_.ant-radio-inner::after]:scale-50`;

export const UniCheckboxStyle = `
[&_.ant-checkbox-wrapper]:text-Title.regular
[&_.ant-checkbox-wrapper]:min-h-14
[&_.ant-checkbox-wrapper]:p-4
[&_.ant-checkbox-wrapper]:mr-0
[&_.ant-checkbox-wrapper]:rounded-xl
[&_.ant-checkbox-wrapper]:bg-ct/checkbox/main/container/default
[&_.ant-checkbox-wrapper]:border-ct/option/stroke/default
[&_.ant-checkbox-wrapper]:items-center
[&_.ant-checkbox-wrapper>*:nth-child(2)]:w-full
[&_.ant-checkbox-wrapper>*:nth-child(2)]:pl-3
[&_.ant-checkbox-wrapper>*:nth-child(2)]:pr-0
[&_.ant-checkbox-wrapper.ant-checkbox-wrapper-checked]:bg-ct/option/container/selected
[&_.ant-checkbox-wrapper.ant-checkbox-wrapper-checked]:text-ct/text/inverse/general
[&_.ant-checkbox-wrapper.ant-checkbox-wrapper-checked]:text-Title.medium
[&_.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-checked):hover]:text-ct/text/moderate/general
[&_.ant-checkbox-wrapper:not(&_.ant-checkbox-wrapper-checked):hover_.ant-checkbox-inner]:border-ct/checkbox/main/focus-outline/hover
[&_.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover_.ant-checkbox-checked:not(.ant-checkbox-disabled)_.ant-checkbox-inner]:bg-ct/option/container/default
[&_.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover_.ant-checkbox-checked:not(.ant-checkbox-disabled)_.ant-checkbox-inner]:border-ct/checkbox/main/focus-outline/default
[&_.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover_.ant-checkbox-inner]:border-ct/checkbox/main/focus-outline/default
[&_.ant-checkbox-checked_.ant-checkbox-inner]:border-ct/checkbox/main/focus-outline/default
[&_.ant-checkbox_.ant-checkbox-inner]:bg-ct/option/container/default
[&_.ant-checkbox_.ant-checkbox-inner:after]:border-ct/option/container/selected`;

export const UniInputStyle = `
[&_.ant-input]:!text-Body.regular_16
[&_.ant-form-item-control-input_.ant-input]:text-ct/text/main/general
[&_.ant-input-data-count]:mr-4 
[&_.ant-input-data-count]:!-bottom-7
[&_.ant-input-textarea-show-count_.ant-input-data-count]:text-ct/text/main/subtlest
[&_.ant-input-suffix]:text-[11px]
[&_.ant-input-show-count-suffix.ant-input-show-count-has-suffix]:!absolute
[&_.ant-input-show-count-suffix.ant-input-show-count-has-suffix]:right-0
[&_.ant-input-show-count-suffix.ant-input-show-count-has-suffix]:bottom-[-26px]
[&_.ant-input-show-count-suffix.ant-input-show-count-has-suffix]:w-[35px]
[&_.ant-input-status-error_.ant-input-show-count-suffix]:text-ct/input-general/main/stroke/error
[&_.ant-input-outlined]:h-14
[&_.ant-input-outlined]:rounded-xl
[&_.ant-input-outlined]:border-ct/input-general/main/stroke/default
[&_.ant-input-outlined.ant-input-status-error]:!border-ct/input-general/main/stroke/error
[&_.ant-input-outlined:hover]:border-ct/input-general/main/stroke/default
[&_.ant-input-outlined:focus-within]:relative
[&_.ant-input-outlined:focus-within]:shadow-none
[&_.ant-input-outlined:focus-within]:border-transparent
[&_.ant-input-affix-wrapper]:after:content-['']
[&_.ant-input-affix-wrapper]:after:pointer-events-none
[&_.ant-input-affix-wrapper]:after:absolute 
[&_.ant-input-affix-wrapper]:after:top-[-5px] 
[&_.ant-input-affix-wrapper]:after:left-[-5px] 
[&_.ant-input-affix-wrapper]:after:right-[-5px] 
[&_.ant-input-affix-wrapper]:after:bottom-[-5px] 
[&_.ant-input-affix-wrapper]:after:border-4 
[&_.ant-input-affix-wrapper]:after:border-transparent 
[&_.ant-input-affix-wrapper]:after:rounded-2xl
[&_.ant-input-affix-wrapper.ant-input-affix-wrapper-focused]:after:border-ct/input-general/main/focus-outline/default
[&_.ant-input-affix-wrapper.ant-input-affix-wrapper-focused.ant-input-status-outlined]:border-ct/input-general/main/focus-outline/default
[&_.ant-input-affix-wrapper.ant-input-affix-wrapper-focused.ant-input-status-outlined]:after:border-ct/input-general/main/focus-outline/default
[&_.ant-input-affix-wrapper.ant-input-affix-wrapper-focused.ant-input-status-success]:border-ct/input-general/main/stroke/success
[&_.ant-input-affix-wrapper.ant-input-affix-wrapper-focused.ant-input-status-success]:after:border-ct/input-general/main/focus-outline/success

[&_.ant-input-affix-wrapper.ant-input-affix-wrapper-focused.ant-input-status-success.no-revalidate-on-re-focused]:border-ct/input-general/main/focus-outline/default
[&_.ant-input-affix-wrapper.ant-input-affix-wrapper-focused.ant-input-status-success.no-revalidate-on-re-focused]:after:border-ct/input-general/main/focus-outline/default

[&_textarea.ant-input]:resize-none
[&_.ant-input-textarea-show-count.ant-input-out-of-range_.ant-input-data-count]:text-ct/input-general/main/stroke/error
[&_.ant-input-affix-wrapper.ant-input-affix-wrapper-focused.ant-input-status-error]:after:border-ct/input-general/main/focus-outline/error
[&_.ant-input-outlined.ant-input-out-of-range:focus-within]:border-ct/input-general/main/stroke/error
[&_.ant-input-affix-wrapper.ant-input-out-of-range.ant-input-affix-wrapper-focused]:after:border-ct/input-general/main/focus-outline/error`;

export const UniFormItemStyle = `
[&_.ant-form-item-feedback-icon]:!text-2xl 
[&_.ant-form-item-required-mark-optional]:!text-ct/text/main/subtle
[&_.ant-form-item-required-mark-optional]:!text-Subtitle.bold 
[&_.ant-form-item-explain-error]:px-4 
[&_.ant-form-item-explain-error]:text-[11px] 
[&_.ant-form-item-explain-error]:mt-2
[&_.ant-form-item_.ant-form-item-explain-error]:text-ct/text/danger/general
[&_.ant-form-item_.ant-form-item-label_>label]:text-ct/text/main/subtle
[&_.ant-form-item_.ant-form-item-label_>label]:text-Subtitle.bold`;

export const UniSelectStyle = `
[&_.ant-select-outlined]:!h-14
[&_.ant-select-single_.ant-select-selector]:!text-Body.regular_16
[&_.ant-select-selector>.ant-select-selection-wrap>.ant-select-selection-item]:text-ct/text/main/general
[&_.ant-select-single_.ant-select-selector]:rounded-xl 
[&_.ant-select.ant-select-in-form-item.ant-select-focused_.ant-select-selector]:!border-ct/input-general/main/focus-outline/default
[&_.ant-select.ant-select-in-form-item_.ant-select-selector]:!border-ct/input-general/main/stroke/default
[&_.ant-select.ant-select-in-form-item:hover_.ant-select-selector]:!border-ct/input-general/main/stroke/default
[&_.ant-select.ant-select-focused]:before:content-['']
[&_.ant-select.ant-select-focused]:before:pointer-events-none
[&_.ant-select.ant-select-focused]:before:absolute 
[&_.ant-select.ant-select-focused]:before:-top-1
[&_.ant-select.ant-select-focused]:before:-left-1
[&_.ant-select.ant-select-focused]:before:-right-1
[&_.ant-select.ant-select-focused]:before:-bottom-1
[&_.ant-select.ant-select-focused]:before:border-4 
[&_.ant-select.ant-select-focused]:before:rounded-2xl
[&_.ant-select.ant-select-focused_.ant-select-selector]:!shadow-none
[&_.ant-select.ant-select-focused]:before:border-ct/input-general/main/focus-outline/default
[&_.ant-select.ant-select-focused.ant-select-status-success]:before:border-ct/input-general/main/focus-outline/default
[&_.ant-select.ant-select-focused.ant-select-status-error]:before:border-ct/input-general/main/focus-outline/error
[&_.ant-select.ant-select-in-form-item.ant-select-status-error:hover_.ant-select-selector]:!border-ct/input-general/main/stroke/error
[&_.ant-select.ant-select-in-form-item.ant-select-status-error_.ant-select-selector]:!border-ct/input-general/main/stroke/error`;

export const UniSelectPopupStyle = `
!px-2 !py-[6px]
[&_.ant-select-item.ant-select-item-option.ant-select-item-option-active]:hover:!bg-ct/menu-general/unit/hover
[&_.ant-select-item.ant-select-item-option.ant-select-item-option-active]:bg-transparent
[&_.ant-select-item.ant-select-item-option.ant-select-item-option-active]:text-ct/text/main/subtlest
[&_.ant-select-item.ant-select-item-option.ant-select-item-option-active]:hover:text-ct/text/main/general
[&_.ant-select-item.ant-select-item-option]:text-ct/text/main/subtlest
[&_.ant-select-item.ant-select-item-option]:leading-8
[&_.ant-select-item.ant-select-item-option.ant-select-item-option-selected]:font-normal
[&_.ant-select-item.ant-select-item-option.ant-select-item-option-selected]:bg-transparent
[&_.ant-select-item.ant-select-item-option.ant-select-item-option-selected]:hover:text-ct/text/moderate/general
[&_.ant-select-item.ant-select-item-option.ant-select-item-option-selected]:text-ct/text/moderate/general
`;
export type FieldStatus = "success" | "error" | "validating" | undefined;

export const GetInputIcon = (fieldStatus: FieldStatus) => {
  switch (fieldStatus) {
    case "success":
      return <CheckCircleFilled className="h-6 w-6 [&>path]:fill-ct/icon/success/general" />;
    case "error":
      return <WarningFilled className="h-6 w-6 [&>path]:fill-ct/icon/danger/general" />;
    case "validating":
      return <LoadingOutlined className="h-6 w-6 text-ct/icon/success/general" />;
    default:
      return <span />;
  }
};

export const UpdatedStatuses = (changedFields: FieldData[]) => {
  return changedFields.reduce<{ [key: string]: FieldStatus }>((acc, field) => {
    acc[field.name[0] as string] = field.errors?.length
      ? "error"
      : field.validating
        ? "validating"
        : "success";
    const checkStatus = acc[field.name[0] as string];
    acc[field.name[0] as string] =
      field.value === "" && checkStatus === "success" ? undefined : acc[field.name[0] as string];
    return acc;
  }, {});
};

export const CustomizeRequiredMark = (
  label: React.ReactNode,
  { required }: { required: boolean },
) => (
  <>
    {label}{" "}
    {required ? (
      <span className="absolute right-0 top-[-2px] ml-1 scale-[.8] font-bold text-ct/text/danger/general">
        ＊
      </span>
    ) : (
      ""
    )}
  </>
);
