"use client";

import React, { useC<PERSON>back, useEffect, useMemo, useState } from "react";
import type { MenuProps } from "antd";
import { Menu } from "antd";
import { usePathname, useRouter } from "next/navigation";
import { appconfigEnv, sendGTMClick } from "@pcsc/utils";
import {
  Exit,
  Binding,
  HeartOutlined,
  Diary,
  File,
  Barcode,
  Location,
  Users,
  FavStore,
  OpPointOutlined,
  CalendarOutlined,
  CouponOutlined,
} from "@pcsc/icons/feature";
import { PutLogout } from "@pcsc/api";
import { MemberCenterMenuKey, memberCenterMenuLabel } from "../constant";
import { GTMEventFactory } from "@pcsc/gtm-event";

const DEFAULT_SELECTED_KEYS = "MY_COLLECT";
const isCouponEnabled = appconfigEnv("CONFIG_PUBLIC_COUPON_ENABLED");
const reservationEnabled = appconfigEnv("CONFIG_PUBLIC_RESERVATION_ENABLED");

type MenuItem = Required<MenuProps>["items"][number];

interface MenuItemChildConfig {
  text: string;
  key: string;
  icon: React.JSX.Element;
  path?: string;
  testId?: string;
  visible: boolean;
  onClick?: () => void;
}

interface MenuItemConfig {
  title: string;
  key: string;
  children: MenuItemChildConfig[];
}

function createMenuItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
  } as MenuItem;
}

function createChildMenuItem(
  item: MenuItemChildConfig,
  handleClick: (href: string) => void,
): MenuItem {
  return createMenuItem(
    <p
      onClick={() => (item.onClick ? item.onClick() : handleClick(item.path || ""))}
      className="pl-1"
      data-testid={item.testId || ""}
    >
      {memberCenterMenuLabel[item.key] || item.key}
    </p>,
    MemberCenterMenuKey[item.key],
    item.icon,
  );
}

const generateMenuItems = (
  menuConfig: MenuItemConfig[],
  handleClick: (href: string) => void,
): MenuItem[] => {
  return menuConfig.map((menu) => {
    return createMenuItem(
      <p className="pl-4 text-ct/text/main/general">{menu.title}</p>,
      menu.key,
      undefined,
      menu.children
        .filter((item) => item.visible)
        .map((item) => createChildMenuItem(item, handleClick)),
    );
  });
};

const generateSmallMenuItems = (
  menuConfig: MenuItemConfig[],
  handleClick: (href: string) => void,
): MenuItem[] => {
  return menuConfig.flatMap((menu) =>
    menu.children
      .filter(({ visible }) => visible)
      .map((item) =>
        createMenuItem(
          <div
            className="flex flex-col items-center justify-center text-center"
            onClick={() => (item.onClick ? item.onClick() : handleClick(item.path || ""))}
          >
            {item.icon}
            <span className="mt-2 text-pretty text-small.regular">
              {memberCenterMenuLabel[item.key] || item.key}
            </span>
          </div>,
          MemberCenterMenuKey[item.key],
        ),
      ),
  );
};

const menuItemsConfig = [
  {
    title: "我的帳戶",
    key: "account",
    children: [
      {
        key: "MY_COLLECT",
        icon: <HeartOutlined className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/myCollect",
        visible: true,
      },
      {
        key: "MY_POINT",
        icon: <OpPointOutlined className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/myPoint",
        testId: "memberCenter-menu-point",
        visible: true,
      },
      {
        key: "MY_RESERVATION",
        icon: <CalendarOutlined className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/myReservation",
        visible: reservationEnabled,
      },
      {
        key: "MY_COUPON",
        icon: <CouponOutlined className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/myCoupon",
        visible: isCouponEnabled,
      },
      {
        key: "EINVOICE",
        icon: <Diary className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/einvoice",
        testId: "memberCenter-menu-einvoice",
        visible: true,
      },
    ],
  },
  {
    title: "常用設定",
    key: "setting",
    children: [
      {
        key: "MY_FRIEND",
        icon: <Users className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/myFriend",
        testId: "memberCenter-menu-myFriend",
        visible: true,
      },
      {
        key: "MY_STORE",
        icon: <FavStore className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/myStore",
        testId: "memberCenter-menu-myStore",
        visible: true,
      },
      {
        key: "MY_ADDRESS",
        icon: <Location className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/myAddress",
        testId: "memberCenter-menu-myAddress",
        visible: true,
      },
      {
        key: "MOBILE_BARCODE_CARRIER",
        icon: <Barcode className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/mobileBarcodeCarrier",
        visible: true,
      },
    ],
  },
  {
    title: "帳戶管理",
    key: "accountSetting",
    children: [
      {
        key: "MEMBER_INFO",
        icon: <File className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/memberInfo",
        testId: "memberCenter-menu-memberInfo",
        visible: true,
      },
      {
        key: "ID_BINDING",
        icon: <Binding className="h-8 w-8 lg:h-5 lg:w-5" />,
        path: "/memberCenter/idBinding",
        visible: true,
      },
      {
        key: "LOGOUT",
        visible: true,
        icon: <Exit className="h-8 w-8 lg:h-5 lg:w-5" />,
        onClick: () => {
          sendGTMClick("menu_logout");
          GTMEventFactory.create("logout").setContentGroup("登出").push();
          void PutLogout(window.location.origin);
        },
      },
    ],
  },
] as MenuItemConfig[];

const MemberCenterMenu = () => {
  const router = useRouter();
  const path = usePathname();
  const [current, setCurrent] = useState("");

  const handleClick = useCallback((href: string) => {
    router.push(href);
  }, []);

  const items: MenuItem[] = useMemo(
    () => generateMenuItems(menuItemsConfig, handleClick),
    [handleClick],
  );
  const itemsSM: MenuItem[] = useMemo(
    () => generateSmallMenuItems(menuItemsConfig, handleClick),
    [handleClick],
  );

  const onClick: MenuProps["onClick"] = useCallback((e) => {
    setCurrent(e.key);
  }, []);

  useEffect(() => {
    setCurrent(path.substring(path.lastIndexOf("/") + 1));
  }, [path]);

  const menuStyleClass = `!text-Title.medium !bg-transparent [&_.ant-menu-submenu_>.ant-menu-submenu-title]:!h-[45px] [&_.ant-menu-inline_.ant-menu-item]:h-[45px] [&_.ant-menu-inline_.ant-menu-item]:!leading-[45px] [&_.ant-menu_.ant-menu-item]:!rounded-xl [&_.ant-menu_.ant-menu-item]:!rounded-l-none [&_.ant-menu_.ant-menu-item]:text-ct/text/main/subtle [&_.ant-menu-submenu-title:active]:!bg-transparent [&_.ant-menu.ant-menu-sub.ant-menu-inline]:!bg-transparent
  [&_.ant-menu-submenu-open_.ant-menu-submenu-selected_>.ant-menu-submenu-title]:bg-at/black-opacity/thick [&_.ant-menu-submenu_.ant-menu-item-selected]:bg-at/black-opacity/thick
  [&_.ant-menu_.ant-menu-item.ant-menu-item-active]:hover:!bg-at/black-opacity/thick [&_.ant-menu_.ant-menu-item.ant-menu-item-selected]:!bg-at/black-opacity/thick
  [&_.ant-menu-submenu_.ant-menu-item.ant-menu-item-selected]:text-ct/text/main/general [&_.ant-menu-submenu_.ant-menu-item-selected_svg]:text-ct/icon/main/general [&_.ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected):hover]:text-ct/text/main/general
  [&_.ant-menu-item-icon]:text-ct/icon/main/subtle [&_.ant-menu-item-icon]:fill-ct/icon/main/subtle [&_.ant-menu-item-selected_.ant-menu-item-icon]:fill-ct/icon/main/general
  [&_.ant-menu_.ant-menu-item.ant-menu-item-active:not(.ant-menu-item-selected)]:hover:!text-ct/text/main/subtle
  [&_.ant-menu-item]:!mb-2  [&_.ant-menu-item]:!rounded-r-lg [&_.ant-menu-submenu-arrow]:mt-[3px] 
  [&_.ant-menu-submenu-arrow]:!origin-center [&_.ant-menu-submenu-arrow]:!scale-125 [&_.ant-menu-submenu-arrow]:!text-ct/icon/main/general [&_.ant-menu-submenu-open_.ant-menu-submenu-arrow]:-mt-1 
  [&_.ant-menu-submenu-title]:!mb-2 [&_.ant-menu-submenu-title]:!rounded-r-lg`;

  const menuMbStyleClass = `!bg-transparent [&_.ant-menu-item]:!h-[78px] [&_.ant-menu-item]:!m-0 [&_.ant-menu-item]:!p-0 [&_.ant-menu-item-selected_.ant-menu-title-content]:text-ct/text/main/general [&_.ant-menu-item-selected_.ant-menu-title-content]:fill-ct/text/main/general [&_.ant-menu-item:not(.ant-menu-item-selected):active]:!bg-at/black-opacity/thick [&_.ant-menu-item]:!rounded-xl [&_.ant-menu-item]:!rounded-l-none
  [&_.ant-menu-item]:!outline-0 [&_.ant-menu-title-content:hover]:fill-ct/text/main/general [&_.ant-menu-title-content:hover]:text-ct/text/main/general
  [&_.ant-menu-title-content]:text-ct/text/main/subtle [&_.ant-menu-title-content]:fill-ct/text/main/subtle [&_.ant-menu-item.ant-menu-item-selected]:bg-at/black-opacity/thick
  `;

  return (
    <>
      <Menu
        inlineIndent={16}
        defaultSelectedKeys={[DEFAULT_SELECTED_KEYS]}
        selectedKeys={[current]}
        defaultOpenKeys={["account", "setting", "accountSetting"]}
        mode="inline"
        items={items}
        onClick={onClick}
        className={`${menuStyleClass} hidden !border-none lg:block`}
      />
      <Menu
        inlineIndent={0}
        defaultOpenKeys={[DEFAULT_SELECTED_KEYS]}
        mode="inline"
        items={itemsSM}
        onClick={onClick}
        className={`${menuMbStyleClass} hidden !border-none xs:block lg:hidden`}
        selectedKeys={[current]}
      />
    </>
  );
};

export default MemberCenterMenu;
