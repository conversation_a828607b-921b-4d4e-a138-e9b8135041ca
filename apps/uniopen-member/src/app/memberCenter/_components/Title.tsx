"use client";
import { type ReactNode } from "react";

type PageTitleProps = {
  title?: ReactNode | string;
  subTitle?: ReactNode | string;
  rightSide?: ReactNode | string;
};

const PageTitle: React.FC<PageTitleProps> = ({
  title = "",
  subTitle: subTitleProps = "",
  rightSide = "",
}: PageTitleProps) => {
  return (
    <div className="flex justify-between">
      <div className="flex flex-col">
        <h3 className="text-H6.bold text-ct/text/main/general xs:text-H4.bold">{title}</h3>
        <div
          className={`text-Body.regular_14 text-ct/text/main/subtle xs:text-Body.regular_16 ${subTitleProps ? "mt-1.5" : ""}`}
        >
          {subTitleProps}
        </div>
      </div>
      {rightSide ? rightSide : ""}
    </div>
  );
};

export default PageTitle;
