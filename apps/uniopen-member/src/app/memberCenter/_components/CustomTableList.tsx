import { Down } from "@pcsc/icons/directional";
import { DotsVertical } from "@pcsc/icons/feature";
import { LoadingBgTransparent } from "@pcsc/icons/others";
import { Menu, type MenuProps, Dropdown, ConfigProvider, Checkbox, Empty } from "antd";
import React, { type ReactNode, useState } from "react";
import EditingBlock from "./EditingBlock";
import { Pagination } from "@pcsc/antd-ui-components";
import type { CustomTableListProps } from "./type";
import { cn } from "@pcsc/tailwind-config/utils";

const DropdownMenu = <T extends object & { id: string }>({
  dropdownItems,
  target,
  setOpen,
}: {
  dropdownItems: { label: string; key: string; action: Function }[];
  target: T;
  setOpen: (open: boolean) => void;
}): React.ReactNode => {
  return (
    <div className="top-1 rounded-xl bg-ct/menu-tab/unit/default py-2 shadow-[0_2px_8px_0px_rgba(0,0,0,0.25)]">
      {dropdownItems.map((item) => (
        <button
          type="button"
          key={`tab_${target.id}_${item.key}`}
          className={cn([
            "block cursor-pointer px-4 py-2 text-button.md text-ct/text/main/subtlest",
            "hover:bg-ct/menu-tab/unit/hover hover:text-ct/text/inverse/general",
            "active:bg-ct/menu-tab/unit/hover active:text-ct/text/inverse/general",
          ])}
          onClick={(e) => {
            e.stopPropagation();
            item.action(target);
            setOpen(false);
          }}
        >
          <p>{item.label}</p>
        </button>
      ))}
    </div>
  );
};

const CustomDropdown = <T extends object & { id: string }>({
  dropdownItems,
  item,
}: {
  dropdownItems: { key: string; label: string; action: Function }[];
  item: T;
}): React.ReactNode => {
  const [open, setOpen] = useState<boolean>(false);
  return (
    <Dropdown
      open={open}
      onOpenChange={() => {
        setOpen(!open);
      }}
      dropdownRender={() => (
        <DropdownMenu<T> dropdownItems={dropdownItems} target={item} setOpen={setOpen} />
      )}
      trigger={["click"]}
      placement="bottom"
      align={{ offset: [2, 4] }}
    >
      <button
        type="button"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <DotsVertical className="inline-block h-6 w-6 [&>path]:fill-ct/icon/moderate/general" />
      </button>
    </Dropdown>
  );
};

const CustomTableList = <T extends object & { id: string }>({
  isLoading,
  tableData,
  labelField,
  subLabelField,
  options,
  dropdownItems,
  className,
  selection,
  page,
  pageSize = 10,
  setPage,
  listEmpty,
  config,
}: CustomTableListProps<T>) => {
  const [expanding, setExpanding] = useState<string[]>([]);

  const handleSelected = (id: string): void => {
    const temp = selection ? [...selection.selected] : [];
    if (!temp.includes(id)) {
      temp.push(id);
    } else {
      temp.splice(temp.indexOf(id), 1);
    }
    selection?.handler(temp);
  };

  type MenuItem = Required<MenuProps>["items"][number];
  const Content = ({ tableData }: { tableData: T }) => {
    return (
      <div className="mt-4 flex flex-col gap-3">
        {Object.entries(tableData).map(([key, value], idx) => {
          if (key === "id" || key === labelField || key === subLabelField) return;
          const option = options[key as keyof T];
          if (option.hidden) return;
          return (
            <div key={`itemContent-${idx}`}>
              <div className="text-Subtitle.bold text-ct/text/main/subtlest">{option.header}</div>
              <p className="w-full whitespace-normal text-Body.regular_16 text-ct/text/main/general">
                {value}
              </p>
            </div>
          );
        })}
      </div>
    );
  };

  const EditableLabel = ({ item }: { item: T }) => {
    const [edit, setEdit] = useState<boolean>(false);
    if (!options[labelField].edit)
      return (
        <div className="flex grow items-center justify-between gap-0">
          <div className="max-w-[244px] overflow-hidden whitespace-normal break-words text-Body.regular_16 text-ct/text/main/general">
            {`${item[labelField]}`}
          </div>
          {subLabelField && (item[subLabelField] as ReactNode)}
        </div>
      );
    return (
      <>
        {edit ? (
          <div>
            <EditingBlock
              dataIndex={labelField as string}
              originalValue={item}
              closeEdit={() => setEdit(false)}
              options={options}
            />
          </div>
        ) : (
          // TBD: use long press ? use click ? icon click?
          <div
            onClick={(e) => {
              e.stopPropagation();
              setEdit(true);
            }}
          >{`${item[labelField]}`}</div>
        )}
      </>
    );
  };

  const generateItems = (tableData: T[]): MenuItem[] => {
    const menuData = tableData.map((item) => {
      if (!item.id || item.id.includes("none")) return;
      return {
        key: item.id,
        label: (
          <div className={`flex ${dropdownItems ? "gap-4" : ""} `}>
            {selection ? (
              <Checkbox
                checked={selection.selected.includes(item.id)}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                onChange={() => {
                  handleSelected(item.id);
                }}
              />
            ) : null}
            <EditableLabel item={item} />
            {/* <div>{`${item[labelField]}`}</div> */}
            <div className={`${dropdownItems ? "min-w-6" : ""}`}>
              <div className="absolute right-3 flex h-full items-center justify-center gap-3 bg-at/white-opacity/general">
                <Down
                  className="inline-block h-6 w-6 transition [&>path]:fill-ct/icon/moderate/general"
                  style={{
                    transform: `rotate(${expanding.includes(String(item.id)) ? 180 : 0}deg)`,
                  }}
                />
                {dropdownItems ? (
                  <CustomDropdown<T> dropdownItems={dropdownItems} item={item} />
                ) : null}
              </div>
            </div>
          </div>
        ),
        children: [
          {
            key: `${item.id}list`,
            label: <Content key={`menuContent-${item.id}`} tableData={item} />,
          },
        ],
      };
    });
    return menuData as MenuItem[];
  };
  const menuItems = generateItems(tableData);
  if (menuItems.length === 0) {
    return (
      <div className={`${className} h-[442px] rounded-xl bg-at/white-opacity/general py-7`}>
        {listEmpty ? listEmpty : <Empty description="No Data" />}
      </div>
    );
  }

  return (
    <div className={`${className} relative`}>
      <div
        className="absolute z-10 h-full w-full items-center justify-center"
        style={{ display: isLoading ? "flex" : "none" }}
      >
        <LoadingBgTransparent
          className="h-[150px] w-full [&_circle]:fill-gray-300"
          style={{ background: "none" }}
        />
      </div>
      <ConfigProvider
        theme={{
          components: {
            Menu: {
              itemActiveBg: "#fff",
              itemHoverBg: "#fff",
              subMenuItemBg: "#fff",
              itemHeight: config?.itemHeight ?? undefined,
            },
          },
        }}
      >
        <Menu
          mode="inline"
          expandIcon={null}
          selectable={false}
          onOpenChange={(e) => {
            setExpanding(e);
          }}
          inlineIndent={0}
          items={menuItems.slice((page - 1) * pageSize, page * pageSize)}
          className={cn(
            "min-h-[580px] rounded-xl [&_.ant-checkbox-checked:hover_.ant-checkbox-inner]:!bg-ct/checkbox/main/container/selected [&_.ant-checkbox-checked_.ant-checkbox-inner]:!border-ct/checkbox/main/container/selected [&_.ant-checkbox-checked_.ant-checkbox-inner]:bg-ct/checkbox/main/container/selected [&_.ant-checkbox:hover_.ant-checkbox-inner]:!border-ct/checkbox/main/focus-outline/default [&_.ant-menu-item]:!h-fit [&_.ant-menu-item_.ant-menu-title-content]:pr-0 [&_.ant-menu-sub_.ant-menu-item]:m-0 [&_.ant-menu-submenu-title]:!m-0 [&_.ant-menu-submenu]:py-4 [&_.ant-menu-title-content]:px-4",
            selection ? "[&_.ant-menu-item_.ant-menu-title-content]:pl-12" : undefined,
          )}
        />
      </ConfigProvider>
      <div className="mt-3 flex w-full justify-end">
        {tableData.length > pageSize && (
          <Pagination
            current={page}
            setPageHandler={(page: number) => {
              setPage(page);
            }}
            count={tableData.length}
            pageSize={pageSize}
          />
        )}
      </div>
    </div>
  );
};

export default CustomTableList;
