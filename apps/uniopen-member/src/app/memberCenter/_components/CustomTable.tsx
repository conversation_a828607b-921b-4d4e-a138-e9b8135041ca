import React, { useEffect, useState } from "react";
import { LoadingBgTransparent } from "@pcsc/icons/others";
import { Table, Tooltip } from "antd";
import type { TableColumnsType } from "antd";
import EditingBlock from "./EditingBlock";
import type { CustomTableProps } from "./type";

const CustomTable = <T extends object & { id: string }>({
  isLoading,
  tableData,
  options,
  actionNode,
  selection,
  className,
  page,
  pageSize = 10,
  setPage,
  tableEmpty,
}: CustomTableProps<T>) => {
  const generateColumn = (): TableColumnsType<T> => {
    const columnArr: TableColumnsType<T> = Object.keys(options).reduce(
      (acc: TableColumnsType<T>, cur: string, index) => {
        if (cur !== "id") {
          const option = options[cur as keyof T];
          acc.push({
            title: option.header || cur,
            dataIndex: cur,
            key: cur,
            ...option,
            width: option?.width ? option.width + (index === 1 ? 20 : 24) : undefined,
          });
        }
        return acc;
      },
      [],
    );
    if (actionNode) {
      const countButtons = actionNode.type.toString().match(/type\s*:\s*"button"/g)?.length ?? 2; // regex "type: 'button'", default 2 actions
      columnArr.push({
        title: <div className="table-action flex justify-end">動作</div>,
        key: "operation",
        fixed: "right",
        width: countButtons * 24 + (countButtons - 1) * 24 + 40,
        render: (text, _, index) => {
          if (!text.id || text.id.includes("none")) return;
          return React.cloneElement(actionNode, {
            rowData: { ...text },
            index: index,
          });
        },
      });
    }
    return columnArr;
  };
  const columns = generateColumn();

  interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
    dataIndex: string;
    record: T;
    children: React.ReactNode;
  }

  const EditableCell: React.FC<EditableCellProps> = ({
    dataIndex,
    record,
    children,
    ...restProps
  }) => {
    const [edit, setEdit] = useState<boolean>(false);
    const width = options[dataIndex as keyof T]?.width;
    const isEditable = !!options[dataIndex as keyof typeof options]?.edit;
    return (
      <td
        {...restProps}
        style={{
          ...restProps.style,
          height: "66px",
          ...(width ? { width: `${width}px` } : {}),
        }}
        onDoubleClick={() => {
          if (isEditable && !edit) {
            setEdit(true);
          }
        }}
      >
        {edit ? (
          <EditingBlock
            dataIndex={dataIndex}
            originalValue={record}
            closeEdit={() => setEdit(false)}
            options={options}
          />
        ) : isEditable ? (
          <Tooltip
            className="cursor-pointer"
            title="滑鼠雙擊可編輯文字"
            color={"#FFFFFF"}
            styles={{ body: { color: "black" } }}
          >
            {children}
          </Tooltip>
        ) : (
          children
        )}
      </td>
    );
  };

  const mergedColumns: any = columns.map((col) => {
    return {
      ...col,
      onCell: (record: T) => ({
        dataIndex: col.key,
        record,
      }),
    };
  });

  const customRowClass = (record: any) => {
    if (record.id?.includes("none"))
      return "!pointer-events-none [&_.ant-table-selection-column_>_label]:hidden";
    return "";
  };

  useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = `
      .ant-table-thead > tr::after {
        content: '';
        position: absolute;
        height: 1px;
        width: calc(100% - 34px);
        background: rgba(0, 0, 0, 0.15);
        display: block;
        left: 17px;
        top: 57px;
        z-index: 3;
      }
      .ant-table-ping-right .ant-table-container .ant-table-thead > tr::after {
        width: ${actionNode ? "calc(100% - 77px)" : ""};
        z-index: 0;
      }
      .ant-table-wrapper .ant-table-container table>thead>tr:first-child >*:last-child {
        border: none;
      }
      .table-action {
        position: relative;
      }
      .ant-table-ping-right .table-action:before {
        content: '';
        position: absolute;
        height: 1px;
        left: 0;
        right: 20px;
        bottom: -20px;
        background: rgba(0, 0, 0, 0.15);
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={`relative ${className}`}>
      <div
        className="absolute z-10 h-full w-full items-center justify-center"
        style={{ display: isLoading ? "flex" : "none" }}
      >
        <LoadingBgTransparent className="h-20 w-full [&_circle]:fill-gray-300" />
      </div>
      <Table
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        columns={mergedColumns}
        dataSource={tableData || []}
        rowKey={(record) => record.id}
        locale={{
          emptyText: tableEmpty,
        }}
        scroll={{ x: "max-content" }}
        {...(selection && {
          rowSelection: {
            type: "checkbox",
            selectedRowKeys: selection.selected,
            onChange: selection.handler,
            getCheckboxProps: ({ id }: { id: React.Key }) => ({
              disabled: typeof id === "string" && id.startsWith("none"), // Column configuration not to be checked
            }),
          },
        })}
        pagination={
          tableData.length > pageSize
            ? {
                position: ["bottomRight"],
                pageSize: pageSize,
                total: tableData.length,
                current: page,
                onChange: (page: number) => setPage(page),
              }
            : false
        }
        rowClassName={customRowClass}
        className={`[&_.ant-checkbox-checked:hover_.ant-checkbox-inner]:!bg-ct/checkbox/main/container/selected [&_.ant-checkbox-checked_.ant-checkbox-inner]:border-ct/checkbox/main/container/selected [&_.ant-checkbox-checked_.ant-checkbox-inner]:!bg-ct/checkbox/main/container/selected [&_.ant-checkbox-indeterminate_.ant-checkbox-inner:after]:bg-ct/checkbox/main/container/selected [&_.ant-checkbox-wrapper:hover_.ant-checkbox-inner]:!border-ct/checkbox/main/focus-outline/hover [&_.ant-empty-description]:mb-[100px] [&_.ant-pagination-item-active_a]:!bg-ct/pagination-unit/container/selected [&_.ant-pagination-item-active_a]:!text-ct/text/inverse/general [&_.ant-pagination-item:hover_a]:!bg-ct/pagination-unit/container/active [&_.ant-pagination-item:hover_a]:!text-ct/text/inverse/general [&_.ant-pagination-item]:!border-transparent [&_.ant-pagination-item_a]:rounded [&_.ant-pagination_.ant-pagination-item:not(.ant-pagination-item-active):hover]:bg-transparent [&_.ant-table-cell.ant-table-cell-row-hover]:!bg-ct/table/unit/hover [&_.ant-table-cell:before]:!bg-transparent [&_.ant-table-row]:!text-Body.regular_16 [&_.ant-table-selection-column]:!pe-0 [&_.ant-table-selection-column]:!ps-5 [&_.ant-table-tbody_>tr.ant-table-row-selected:hover_>td.ant-table-cell]:!bg-ct/table/unit/hover [&_.ant-table-tbody_>tr.ant-table-row-selected_>td.ant-table-cell]:!bg-ct/table/container [&_.ant-table-tbody_>tr_>td]:!border-0 [&_.ant-table-thead_.ant-table-cell]:h-[58px] [&_.ant-table-thead_>tr_>th]:!border-0 [&_.ant-table-thead_>tr_>th]:!bg-ct/checkbox/main/container/default [&_.ant-table-thead_>tr_>th]:!text-Subtitle.bold [&_.ant-table-thead_>tr_>th]:!text-ct/text/main/subtlest [&_.ant-table]:!rounded-xl [&_.ant-table]:!font-notoSansTc [&_.ant-table]:[clip-path:border-box]`}
      />
    </div>
  );
};

export default CustomTable;
