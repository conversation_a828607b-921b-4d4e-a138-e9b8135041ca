import { Check, Cross } from "@pcsc/icons/feature";
import { Input } from "antd";
import React, { useState } from "react";
import { UniInputStyle } from "./FormStyle";

const EditingBlock = <T,>({
  dataIndex,
  originalValue,
  closeEdit,
  options,
}: {
  dataIndex: string;
  originalValue: T;
  closeEdit: Function;
  options: any;
}) => {
  const [value, setValue] = useState<string>(
    originalValue[dataIndex as keyof typeof originalValue] as string,
  );
  const option = options[dataIndex as keyof typeof options];
  const submitThenClose = async (value: string, dataIndex: any) => {
    const newRowValue = { ...originalValue, [dataIndex]: value };
    await option.edit.submitHandler(newRowValue);
    closeEdit();
  };

  const Icons = () => {
    return (
      <div className="flex items-center">
        <Check
          className="h-5 w-5 cursor-pointer [&>path]:fill-ct/icon/main/general"
          onClick={(e: React.MouseEvent<HTMLElement>) => {
            e.stopPropagation();
            submitThenClose(value, dataIndex);
          }}
        />
        <div className="mx-1 h-6 w-px bg-ct/checkbox/main/focus-outline/default" />
        <Cross
          className="h-5 w-5 cursor-pointer [&>path]:fill-ct/icon/main/general"
          onClick={(e: React.MouseEvent<HTMLElement>) => {
            e.stopPropagation();
            closeEdit();
          }}
        />
      </div>
    );
  };

  return (
    <Input
      count={{
        max: option.edit.maxLength,
        strategy: (txt) => txt.length,
        exceedFormatter: (txt, { max }) => txt.slice(0, max),
      }}
      defaultValue={value}
      suffix={<Icons />}
      onChange={(e) => setValue(e.target.value)}
      onClick={(e) => e.stopPropagation()}
      className={UniInputStyle}
    />
  );
};

export default EditingBlock;
