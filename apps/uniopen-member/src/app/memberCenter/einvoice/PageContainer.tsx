"use client";

import { useQuery } from "@tanstack/react-query";
import { getEinvoiceIframeUrl } from "@pcsc/api";
import { LoadingBgTransparent } from "@pcsc/icons/others";
import MemberCenterNoResultError from "../_components/MemberCenterNoResultError";
import { Dialog } from "@pcsc/ui-components";
import { useEffect, useRef, useState } from "react";
import { cn } from "@pcsc/tailwind-config/utils";
import { useMediaQuery } from "usehooks-ts";

const getBrowserInfo = () => {
  const userAgent = navigator.userAgent;

  // 判斷是否為 Safari（排除 Chrome）
  const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);

  // 判斷是否為行動裝置
  const isMobile = /iPhone|iPad|iPod|Android/i.test(userAgent);

  return { isSafari, isMobile };
};

const useBrowserInfo = () => {
  return useQuery({
    queryKey: ["browserInfo"],
    queryFn: getBrowserInfo,
    staleTime: Infinity,
  });
};

const EinvoiceIframe = ({ src }: { src: string }) => {
  const { data: browser } = useBrowserInfo();
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const dialogContainerRef = useRef<HTMLDivElement>();
  const isPhone = useMediaQuery("(max-width: 480px)");
  const dialogProps = {
    title: "無法顯示「發票日誌」",
    subTitle: "請確認你的 Safari 設定已允許跨網站追蹤，以正常瀏覽：",
    isOpen: isDialogOpen,
    setIsOpen: setIsDialogOpen,
    size: isPhone ? "s" : ("m" as "s" | "m"),
    rightButton: {
      children: "我知道了",
      onClick: () => setIsDialogOpen(false),
    },
    container: dialogContainerRef.current,
    className: isPhone ? "" : "max-[601px]:max-w-[290px]",
  };

  useEffect(() => {
    if (browser) {
      setIsDialogOpen(browser.isSafari);
    }
  }, [browser]);

  return (
    <div className="relative">
      <div ref={dialogContainerRef}></div>
      <Dialog {...dialogProps}>
        <ol
          className={cn(
            "list-decimal text-ct/text/main/subtle",
            isPhone ? "pl-4 text-Body.regular_14" : "pl-6 text-Body.regular_16",
          )}
        >
          {browser?.isMobile ? (
            <>
              <li>手機設定 &gt; App &gt; Safari &gt; 隱私權與安全性</li>
              <li>關閉「防止跨網站追蹤」</li>
              <li>重新開啟 Safari 後再試一次</li>
            </>
          ) : (
            <>
              <li>點擊左上方 Safari &gt; 設定 &gt; 隱私權</li>
              <li>取消勾選「防止跨網站追蹤」</li>
              <li>重新整理頁面後再試一次</li>
            </>
          )}
        </ol>
      </Dialog>
      <iframe
        title="einvoice"
        name="einvoiceIframe"
        id="einvoiceIframe"
        className="aspect-[3_/_4] h-auto w-full"
        src={src}
        sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
      />
    </div>
  );
};

const PageContainer = () => {
  const {
    data,
    refetch: EinvoiceIframeUrlRefetch,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [`getEinvoiceIframeUrl`],
    queryFn: () => getEinvoiceIframeUrl(),
    staleTime: 0,
    gcTime: 0,
  });

  if (isLoading) {
    return (
      <LoadingBgTransparent
        className="h-20 w-full [&_circle]:fill-gray-300"
        style={{ background: "none" }}
      />
    );
  }

  const iframeUrl = data?.url;
  if (isError || (!iframeUrl && !isLoading)) {
    return <MemberCenterNoResultError clickHandler={() => EinvoiceIframeUrlRefetch()} />;
  }

  return iframeUrl ? <EinvoiceIframe src={iframeUrl} /> : null;
};

export default PageContainer;
