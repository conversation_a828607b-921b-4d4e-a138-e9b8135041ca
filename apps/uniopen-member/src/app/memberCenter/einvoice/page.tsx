import { type Metadata } from "next";
import PageTitle from "../_components/Title";
import { memberCenterMenuLabel } from "../constant";
import PageContainer from "./PageContainer";

export function generateMetadata(): Metadata {
  return {
    title: `歡迎來到 uniopen 會員 - ${memberCenterMenuLabel.EINVOICE}`,
  };
}

export default function EinvoicePage() {
  return (
    <>
      <PageTitle title={memberCenterMenuLabel.EINVOICE} />
      <div className="mt-4 min-w-[calc(100%_-_48px)] xs:mt-6">
        <PageContainer />
      </div>
    </>
  );
}
