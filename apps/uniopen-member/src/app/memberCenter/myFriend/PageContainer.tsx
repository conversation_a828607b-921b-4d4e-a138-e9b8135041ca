"use client";
import React, {
  memo,
  useState,
  useMemo,
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  type ReactNode,
} from "react";
import FriendNavBar from "./_components/FriendNavBar";
import dynamic from "next/dynamic";
import { Delete, Pencil } from "@pcsc/icons/feature";
import CustomTableEmpty from "../_components/CustomTableEmpty";
import { type OptionsType } from "../_components/type";
import { NoFriends } from "@pcsc/icons/empty";
import { useWindowSize } from "usehooks-ts";

import { PER_PAGE, useQueryCurrentGroupData, useQueryMemberFriendGroups } from "./_hooks/useQuery";
import { type MyFriendType } from "@pcsc/api";
import { type ModalConfig, ModalContext } from "@pcsc/antd-ui-components";
import { Button } from "@pcsc/ui-components";
import { useMutationAddFriendToGroup, useMutationDeleteFriend } from "./_hooks/useMutation";
import { showConfirmDeleteDialog } from "../_flow/showConfirmDeleteDialog";
import { showErrorToast, showSuccessToast } from "@pcsc/apps-components";
import { DynamicCustomTable, DynamicCustomTableList } from "../_components/DynamicExport";

const DynamicFriendForm = dynamic(() => import("./_components/FriendForm"), {
  ssr: false,
});

const DynamicAddFriendToGroupFooter = dynamic(
  () => import("./_components/AddFriendToGroupFooter"),
  {
    ssr: false,
  },
);

const MyFriendEmpty = ({ buttonClick }: { buttonClick: VoidFunction }) => (
  <div className="animate-fadeIn">
    <CustomTableEmpty
      emptyImage={<NoFriends className="h-40 w-[200px]" />}
      emptyDescription={
        <div className="h-[333px]">
          <div className="mb-6 text-Body.regular_16 text-ct/text/main/subtlest">目前還沒有朋友</div>
          <div className="flex justify-center">
            <Button
              className="min-w-28"
              variant="button-filled"
              color="main"
              size="xl"
              onClick={buttonClick}
            >
              <p className="text-button.xxlg">新增</p>
            </Button>
          </div>
        </div>
      }
    />
  </div>
);

const MemoizedMyFriendEmpty = React.memo(MyFriendEmpty);

type MyFriendHeaderType = OptionsType<MyFriendType>;

const ActionIcons = ({
  rowData,
  editFriendHandler,
  deleteFriendHandler,
  showModal,
  hideModal,
}: {
  // get rowData in table component
  rowData?: MyFriendType;
  editFriendHandler: (rowData: MyFriendType) => void;
  deleteFriendHandler: (id: MyFriendType["id"]) => void;
  showModal: (config: ModalConfig) => void;
  hideModal: () => void;
}): ReactNode => (
  <div className="float-right">
    <div className="flex justify-between gap-6">
      <button
        type="button"
        className="flex cursor-pointer items-center justify-center rounded-full text-center transition hover:bg-ct/button-icon/spotlight/hover"
        onClick={() => {
          if (rowData) editFriendHandler(rowData);
        }}
      >
        <Pencil className="h-6 w-6 [&>path]:fill-ct/icon/moderate/general" />
      </button>
      <button
        type="button"
        className="flex cursor-pointer items-center justify-center rounded-full text-center transition hover:bg-ct/button-icon/spotlight/hover"
        onClick={() => {
          if (rowData) {
            showConfirmDeleteDialog("確定要刪除朋友？", showModal, hideModal, () => {
              deleteFriendHandler(rowData.id);
            });
          }
        }}
      >
        <Delete className="h-6 w-6 [&>path]:fill-ct/icon/moderate/general" />
      </button>
    </div>
  </div>
);

const MemoizedActionIcons = memo(ActionIcons);

const myFriendSetting: MyFriendHeaderType = {
  id: {},
  nickname: {
    header: "朋友暱稱",
    width: 288,
  },
  mobileNumber: { header: "手機號碼", width: 104 },
  iCash: { header: "iCash卡號/認證碼" },
};

const PageContainer = () => {
  const { width } = useWindowSize({ debounceDelay: 16 });
  const [selectGroupId, setSelectGroupId] = useState("all");
  const [page, setPage] = useState<number>(1);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const modalContext = useContext(ModalContext);
  const { showModal, hideModal } = modalContext;
  const rowSelectionHandler = useCallback(
    (selectedRowKeys: React.Key[]) => setSelectedKeys(selectedRowKeys),
    [],
  );

  const switchAndFetchGroupData = useCallback(
    (groupId?: string) => {
      if (selectedKeys.length > 0) {
        setSelectedKeys([]);
      }
      if (page !== 1 && groupId !== selectGroupId) {
        setPage(1);
      }
      const nextGroupId = groupId || "all";
      setSelectGroupId(nextGroupId);
    },
    [selectedKeys, page, selectGroupId],
  );

  const { data: memberFriendGroupsData, isFetching: fetchingFriendGroup } =
    useQueryMemberFriendGroups();

  const {
    data: currentTableData,
    isFetching: loadingTableData,
    refetch,
  } = useQueryCurrentGroupData({
    groupId: selectGroupId,
  });

  const { mutate: mutateDeleteFriend } = useMutationDeleteFriend({
    groupId: selectGroupId,
    onSuccess: () => {
      showSuccessToast({ content: "你已成功刪除朋友" });
      refetch();
    },
    onError: () => {
      showErrorToast({ content: "刪除朋友失敗" });
    },
  });

  const { mutate: mutateAddFrinedToGroup } = useMutationAddFriendToGroup();

  const batchAddFriendsToGroup = useCallback(
    ({ selectedKeys, groupId }: { selectedKeys: React.Key[]; groupId: string }) => {
      selectedKeys.map((friendId) => {
        mutateAddFrinedToGroup({ selectGroupId: groupId, friendId: `${friendId}` });
      });
      // need to change api as api is ready
      setTimeout(() => {
        switchAndFetchGroupData(groupId);
      }, 800);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [switchAndFetchGroupData],
  );

  const batchAddFriendToMultiGroups = useCallback(
    ({
      selectedKey,
      groupIds,
      switchToAll = true,
    }: {
      selectedKey: React.Key;
      groupIds: string[];
      switchToAll?: boolean;
    }) => {
      groupIds.map((groupId) => {
        mutateAddFrinedToGroup({ selectGroupId: groupId, friendId: `${selectedKey}` });
      });
      setTimeout(() => {
        switchToAll && switchAndFetchGroupData("all");
      }, 800);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [switchAndFetchGroupData],
  );

  const showAddFriendModalHandler = useCallback(() => {
    showModal({
      title: "新增朋友",
      subTitle: "加入你的朋友可以隨時共享、轉贈、交換點數",
      type: "lgDialog",
      destroyOnClose: true,
      content: (
        <DynamicFriendForm
          currentGroupId={selectGroupId}
          batchAddFriendToMultiGroups={batchAddFriendToMultiGroups}
        />
      ),
      hasDrawer: true,
    });
  }, [showModal, batchAddFriendToMultiGroups, selectGroupId]);

  const showEditFriendModalHandler = useCallback(
    (selectedFriend: MyFriendType) => {
      if (!selectedFriend?.id) {
        return;
      }
      setTimeout(() => {
        showModal({
          title: "編輯朋友",
          subTitle: "可隨時編輯朋友暱稱",
          type: "lgDialog",
          destroyOnClose: true,
          content: (
            <DynamicFriendForm
              editFriendId={selectedFriend.id}
              currentGroupId={selectGroupId}
              batchAddFriendToMultiGroups={batchAddFriendToMultiGroups}
            />
          ),
          hasDrawer: true,
        });
      }, 0);
    },
    [showModal, batchAddFriendToMultiGroups, selectGroupId],
  );

  const dropdownItems = useMemo(
    () => [
      {
        key: "edit",
        label: "編輯",
        action: (selectedFriend: MyFriendType) => {
          showEditFriendModalHandler(selectedFriend);
        },
      },
      {
        key: "delete",
        label: "刪除",
        action: (myFriendItem: MyFriendType) => {
          showConfirmDeleteDialog("確定要刪除這個好友嗎？", showModal, hideModal, () => {
            mutateDeleteFriend(myFriendItem.id);
          });
        },
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [mutateDeleteFriend],
  );

  useEffect(() => {
    if (currentTableData.length && localStorage.getItem("recordModal")) {
      const recordModal: { type: string; value: { id: string } } = JSON.parse(
        localStorage.getItem("recordModal") || "",
      );
      if (recordModal.type === "createFriend") {
        showAddFriendModalHandler();
      } else if (recordModal.type === "editFriend") {
        const contactData = currentTableData?.find((friend) => friend.id === recordModal.value?.id);
        showEditFriendModalHandler(contactData as MyFriendType);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentTableData]);

  return (
    <>
      <FriendNavBar
        friendGroups={memberFriendGroupsData?.friendGroups || []}
        selectGroupId={selectGroupId}
        onChange={switchAndFetchGroupData}
        fetchingFriendGroup={fetchingFriendGroup}
        showAddFriendModalHandler={showAddFriendModalHandler}
      />
      <div className="relative rounded-xl xs:h-[718px] xs:bg-ct/card-basic/main/container">
        {width > 480 ? (
          <DynamicCustomTable
            isLoading={loadingTableData}
            tableData={currentTableData}
            options={myFriendSetting}
            page={page}
            pageSize={PER_PAGE}
            setPage={setPage}
            actionNode={
              <MemoizedActionIcons
                editFriendHandler={showEditFriendModalHandler}
                deleteFriendHandler={mutateDeleteFriend}
                showModal={showModal}
                hideModal={hideModal}
              />
            }
            tableEmpty={<MemoizedMyFriendEmpty buttonClick={showAddFriendModalHandler} />}
            {...(selectGroupId === "all"
              ? {
                  selection: {
                    handler: rowSelectionHandler,
                    selected: selectedKeys,
                  },
                }
              : {})}
          />
        ) : (
          <DynamicCustomTableList
            isLoading={loadingTableData}
            tableData={currentTableData}
            page={page}
            pageSize={PER_PAGE}
            setPage={setPage}
            // @ts-ignore: Unreachable code error
            labelField="nickname"
            options={myFriendSetting}
            dropdownItems={dropdownItems}
            listEmpty={<MemoizedMyFriendEmpty buttonClick={showAddFriendModalHandler} />}
            {...(selectGroupId === "all"
              ? {
                  selection: {
                    handler: rowSelectionHandler,
                    selected: selectedKeys,
                  },
                }
              : {})}
          />
        )}
      </div>
      {selectedKeys.length > 0 && selectGroupId === "all" ? (
        <DynamicAddFriendToGroupFooter
          selectedFriendsKey={selectedKeys}
          friendGroups={memberFriendGroupsData?.friendGroups || []}
          addFriendsToGroup={batchAddFriendsToGroup}
        />
      ) : undefined}
    </>
  );
};

export default PageContainer;
