import { type ReactNode } from "react";
import { But<PERSON> } from "@pcsc/ui-components";
import { type ModalConfig } from "@pcsc/antd-ui-components";

const ConfirmDeleteDialogContent = ({
  hideModal,
  deleteClickHandler,
}: {
  hideModal: () => void;
  deleteClickHandler: () => void;
}): ReactNode => (
  <div data-testid="confirm-delete-dialog-buttons" className="flex justify-end gap-2 pt-5">
    <Button
      onClick={hideModal}
      className="ct/text/moderate/general text-button.xxlg"
      variant="button-stroke"
      color="main"
    >
      取消
    </Button>
    <Button
      data-testid="confirm-delete-button"
      onClick={() => {
        // Dismiss the dialog immediately and let user know the result via toast, then we don't need to handle the fetching state on the delete button
        hideModal();
        deleteClickHandler();
      }}
      className="text-button.xxlg"
      variant="button-filled"
      color="main"
    >
      刪除
    </Button>
  </div>
);

export const showConfirmDeleteDialog = (
  title: string,
  showModal: (config: ModalConfig) => void,
  hideModal: () => void,
  deleteClickHandler: () => void,
  additionalModalConfig: Partial<ModalConfig> = Object.create(null),
): void => {
  showModal({
    title,
    content: (
      <ConfirmDeleteDialogContent hideModal={hideModal} deleteClickHandler={deleteClickHandler} />
    ),
    type: "smDialog",
    hasDrawer: false,
    className: "[&_.ant-modal-content]:!rounded-xl [&_.title]:!text-H6.bold",
    ...additionalModalConfig,
  });
};
