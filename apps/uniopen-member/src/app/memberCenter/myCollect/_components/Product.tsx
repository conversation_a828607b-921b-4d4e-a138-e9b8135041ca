import { useEffect, useState } from "react";
import { useIntersectionObserver } from "usehooks-ts";
import ProductCard from "@/components/Product/ProductCard";
import { LoadingBgTransparent } from "@pcsc/icons/others";

import EmptyCard from "./Empty";
import { useUserStore } from "@pcsc/store-providers";
import { productDataFormat } from "@/utils/dataFormat";
import type { ProductType } from "@pcsc/api";
import type { CommonProductCardType } from "@/components/Product/ProductCard";
import useCollectFromType from "../hook/useCollectFromType";
import { GTMEventFactory } from "@pcsc/gtm-event";
import { env } from "@pcsc/utils";

const CollectProduct = () => {
  const { uniOpen } = useUserStore((state) => state);
  const { updateFavoriteList, favoriteList } = uniOpen;
  const [productList, setProductList] = useState<CommonProductCardType[]>([]);
  const { isIntersecting, ref: loadMoreRef } = useIntersectionObserver({ threshold: 0.5 });

  const updateData = (type: string, itemId: string) => {
    const filterProductList = productList?.filter((item) => {
      return item.product.id !== itemId;
    });
    setProductList(filterProductList);
    const favoriteListId = favoriteList.product?.filter((item) => {
      return item !== itemId;
    });
    if (updateFavoriteList && favoriteListId) {
      updateFavoriteList("product", favoriteListId);
    }
  };

  const { collectionCardList: currentProductData, isLoading } = useCollectFromType<
    ProductType,
    CommonProductCardType
  >("product", 12, "DESC", isIntersecting, productDataFormat);

  useEffect(() => {
    if (currentProductData) {
      setProductList(currentProductData);
    }
  }, [currentProductData]);

  return (
    <>
      <div className="grid grid-cols-2 gap-[24px] smm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4">
        {productList?.map((item, index) => {
          const { product, img } = item;
          const { id, name, internalUrl, productUrl, categories, bu, price, dataSource } = product;
          return (
            <div
              ref={index === productList.length - 1 && !isLoading ? loadMoreRef : undefined}
              key={`product_card_${id}`}
            >
              <ProductCard
                img={img}
                product={product}
                contentWithWhiteBg
                updateProductCardData={updateData}
                gtmShareEvent={GTMEventFactory.create("share")
                  .setClickId(`會員_我的收藏_商品_${index + 1}`)
                  .setClickContent(name)
                  .setClickUrl(
                    internalUrl ? env("NEXT_PUBLIC_SITE_DOMAIN") + internalUrl : productUrl,
                  )
                  .setClickCategory(categories[0])
                  .setClickType("購物_商品")
                  .setBrand(dataSource)}
                gtmSelectItemEvent={GTMEventFactory.create("select_item")
                  .setClickId(`會員_我的收藏_商品_${index + 1}`)
                  .setClickCategory(categories[0] || "")
                  .setBrand(dataSource)
                  .setClickContent(name)
                  .setClickUrl(
                    internalUrl ? env("NEXT_PUBLIC_SITE_DOMAIN") + internalUrl : productUrl,
                  )
                  .setClickType("購物_商品")
                  .setECommerceItem({
                    item_id: id,
                    item_name: name,
                    item_brand: dataSource,
                    item_category: categories[0] || "",
                    price: price,
                  })}
              />
            </div>
          );
        })}
      </div>
      {productList.length === 0 && !isLoading && (
        <EmptyCard link="shopping" text="商品" className={"min-h-[360px]"} />
      )}
      <div className={`${isLoading ? "" : "hidden"} flex items-center justify-center`}>
        <div className="h-20 w-20">
          <LoadingBgTransparent className="h-full w-full [&_circle]:fill-gray-300" />
        </div>
      </div>
    </>
  );
};

export default CollectProduct;
