import React from "react";
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import MyStore from "@/app/memberCenter/myStore/page";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { useUserStore } from "@pcsc/store-providers";
import { ModalProvider } from "@pcsc/antd-ui-components";

// Mock the required modules
jest.mock("@tanstack/react-query");
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));
jest.mock("@pcsc/store-providers", () => ({
  useUserStore: jest.fn(),
}));
jest.mock("@pcsc/apps-components", () => ({
  showSuccessToast: jest.fn(),
}));

// Mock store data
const mockStores = [
  {
    id: "1",
    nickname: "Store 1",
    storeId: "001",
    storeName: "Test Store 1",
    address: "Test Address 1",
    phoneNumber: "**********",
  },
  {
    id: "2",
    nickname: "Store 2",
    storeId: "002",
    storeName: "Test Store 2",
    address: "Test Address 2",
    phoneNumber: "**********",
  },
];

describe("MyStore Page", () => {
  beforeEach(() => {
    // Mock useQuery hook
    (useQuery as jest.Mock).mockReturnValue({
      data: {
        preferences: {
          stores: mockStores,
        },
      },
      refetch: jest.fn(),
      isFetching: false,
    });

    // Mock useMutation hooks
    (useMutation as jest.Mock).mockReturnValue({
      mutate: jest.fn(),
      isPending: false,
    });

    // Mock useSearchParams
    (useSearchParams as jest.Mock).mockReturnValue({
      get: () => null,
    });

    // Mock useUserStore
    (useUserStore as jest.Mock).mockReturnValue({
      uniOpen: {
        isUser: true,
      },
    });
  });

  it("renders the 7-11 store list correctly", async () => {
    render(
      <ModalProvider>
        <MyStore />
      </ModalProvider>,
    );

    await waitFor(() => {
      // <MyStore /> render desktop, mobile list in the same time
      expect(screen.getAllByText("Store 1")[0]).toBeInTheDocument();
      expect(screen.getAllByText("Store 1")[1]).toBeInTheDocument();

      expect(screen.getAllByText("Store 2")[0]).toBeInTheDocument();
      expect(screen.getAllByText("Store 2")[1]).toBeInTheDocument();
    });
  });

  it("displays empty state when no 7-11 stores are available", async () => {
    (useQuery as jest.Mock).mockReturnValue({
      data: {
        preferences: {
          stores: [],
        },
      },
      refetch: jest.fn(),
      isFetching: false,
    });

    render(
      <ModalProvider>
        <MyStore />
      </ModalProvider>,
    );

    await waitFor(() => {
      expect(screen.getAllByText("目前還沒有常用門市")[0]).toBeInTheDocument();
      expect(screen.getAllByText("目前還沒有常用門市")[1]).toBeInTheDocument();
    });
  });

  it("handles 7-11 store deletion", async () => {
    const mockDeleteMutation = jest.fn().mockResolvedValue({ success: true });
    (useMutation as jest.Mock).mockReturnValue({
      mutate: mockDeleteMutation,
      isPending: false,
    });

    render(
      <ModalProvider>
        <MyStore />
      </ModalProvider>,
    );

    // Find and click delete button
    const deleteButton = screen.getAllByTestId("delete")[0];
    fireEvent.click(deleteButton);

    // Confirm delete dialog will show
    expect(await screen.findByTestId("confirm-delete-dialog-buttons")).toBeInTheDocument();
    await act(() => userEvent.click(screen.getByTestId("confirm-delete-button")));

    await waitFor(() => {
      expect(mockDeleteMutation).toHaveBeenCalledWith("1");
    });
  }, 15000);

  it("handles one favorite 7-11 store nickname update", async () => {
    const mockUpdateMutation = jest.fn().mockResolvedValue({ success: true });
    (useMutation as jest.Mock).mockReturnValue({
      mutate: mockUpdateMutation,
      isPending: false,
    });

    render(
      <ModalProvider>
        <MyStore />
      </ModalProvider>,
    );

    // Find and click edit button
    const editButton = screen.getAllByTestId("edit")[0];
    fireEvent.click(editButton);

    // Update nickname in form
    const nicknameInput = await screen.findByTestId("nickname-input");
    await act(async () => {
      await userEvent.clear(nicknameInput);
      await userEvent.type(nicknameInput, "OPEN小將之家");
    });

    // Submit form
    const submitButton = screen.getAllByText("儲存")[0];
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockUpdateMutation).toHaveBeenCalledWith({
        currentId: "1",
        nickname: "OPEN小將之家",
      });
    });
  });
});
