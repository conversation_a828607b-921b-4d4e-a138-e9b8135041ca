import { render, screen, fireEvent } from "@testing-library/react";
import { useSearchParams, notFound } from "next/navigation";
import { appconfigEnv } from "@pcsc/utils";
import MyReservation from "@/app/memberCenter/myReservation/page";

// ===== Mock =====
jest.mock("@/app/memberCenter/myReservation/_components/ReservationComing", () => {
  const MockReservationComing = () => <div data-testid="reservation-coming">Coming Component</div>;
  MockReservationComing.displayName = "MockReservationComing";
  return MockReservationComing;
});
jest.mock("@/app/memberCenter/myReservation/_components/ReservationUsed", () => {
  type Props = { status: string };
  const MockReservationUsed = ({ status }: Props) => (
    <div data-testid={`reservation-used-${status}`}>Used Component: {status}</div>
  );
  MockReservationUsed.displayName = "MockReservationUsed";
  return MockReservationUsed;
});
jest.mock("@/app/memberCenter/_components/Title", () => {
  type Props = { title: string };
  function MockPageTitle({ title }: Props) {
    return <div data-testid="page-title">{title}</div>;
  }
  MockPageTitle.displayName = "MockPageTitle";
  return MockPageTitle;
});
jest.mock("@/components/ScrollableTabs", () => ({ active, tabList, tabChange }) => (
  <div>
    {tabList.map((tab) => (
      <button key={tab.id} onClick={() => tabChange(tab)} data-testid={`tab-${tab.id}`}>
        {tab.title}
      </button>
    ))}
    <div data-testid="active-tab">{active.id}</div>
  </div>
));

// ===== Mock Next.js navigation hooks =====
const mockReplace = jest.fn();
jest.mock("next/navigation", () => ({
  useRouter: () => ({ replace: mockReplace }),
  useSearchParams: jest.fn(),
  notFound: jest.fn(),
}));

// ===== Mock Config =====
jest.mock("@pcsc/utils", () => ({
  appconfigEnv: jest.fn(),
}));

// ===== Helper =====
const setup = (statusParam?: string, enabled = true) => {
  (appconfigEnv as jest.Mock).mockReturnValue(enabled);
  (useSearchParams as jest.Mock).mockReturnValue({
    get: (key: string) => (key === "status" ? (statusParam ?? null) : null),
  });

  return render(<MyReservation />);
};

describe("MyReservation", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders page title", () => {
    setup();
    expect(screen.getByTestId("page-title")).toHaveTextContent("我的預約");
  });

  it("shows ReservationComing by default when no status", () => {
    setup();
    expect(screen.getByTestId("reservation-coming")).toBeInTheDocument();
    expect(screen.getByTestId("active-tab")).toHaveTextContent("coming");
  });

  it("shows used reservation component when status is 'used'", () => {
    setup("used");
    expect(screen.getByTestId("reservation-used-used")).toBeInTheDocument();
  });

  it("shows cancel reservation component when status is 'cancel'", () => {
    setup("cancel");
    expect(screen.getByTestId("reservation-used-cancel")).toBeInTheDocument();
  });

  it("calls router.replace on tab change", () => {
    setup();
    fireEvent.click(screen.getByTestId("tab-used"));
    expect(mockReplace).toHaveBeenCalledWith("/memberCenter/myReservation?status=used");
  });

  it("calls notFound() if reservation is disabled", () => {
    setup("used", false);
    expect(notFound).toHaveBeenCalled();
  });
});
