import "@testing-library/jest-dom";
import { render, screen, waitFor } from "@testing-library/react";
import { GetMemberInformation } from "@pcsc/api";
import IdBindingPage from "@/app/memberCenter/idBinding/page";

jest.mock("@pcsc/api");

// Mock component
jest.mock("@/app/memberCenter/idBinding/_components/IdBindingCard", () => {
  return jest.fn(({ name, id }) => (
    <div data-testid="IdBindingCard">
      {name}: {id}
    </div>
  ));
});

jest.mock("@/app/memberCenter/_components/Title", () => {
  return jest.fn(({ title, subTitle }) => (
    <div data-testid="PageTitle">
      {title} - {subTitle}
    </div>
  ));
});

describe("IdBindingPage", () => {
  const mockIdBindingData = {
    idBinding: {
      bookId: "12345",
      ppcId: "67890",
      duskinId: null,
      organicshopsId: "54321",
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the PageTitle component with correct title and subtitle", () => {
    render(<IdBindingPage />);

    expect(screen.getByTestId("PageTitle")).toHaveTextContent("連結帳號查詢");
    expect(screen.getByTestId("PageTitle")).toHaveTextContent(
      "目前桌機無法修改，請至OPENPOINT APP綁定集團帳號",
    );
  });

  it("should initially render the default resultArray with null IDs", () => {
    render(<IdBindingPage />);

    const cards = screen.getAllByTestId("IdBindingCard");
    expect(cards).toHaveLength(4);

    const expectedNames = ["book", "ppc", "duskin", "organicshops"];
    cards.forEach((card, index) => {
      expect(card).toHaveTextContent(`${expectedNames[index]}:`);
    });
  });

  it("should fetch and update the resultArray after API call", async () => {
    (GetMemberInformation as jest.Mock).mockResolvedValue(mockIdBindingData);

    render(<IdBindingPage />);

    await waitFor(() => expect(GetMemberInformation).toHaveBeenCalledTimes(1));

    // 等待狀態更新後，檢查卡片內容是否正確
    await waitFor(() => {
      const cards = screen.getAllByTestId("IdBindingCard");
      expect(cards).toHaveLength(4);
      expect(cards[0]).toHaveTextContent("book: 12345");
      expect(cards[1]).toHaveTextContent("ppc: 67890");
      expect(cards[2]).toHaveTextContent("duskin:");
      expect(cards[3]).toHaveTextContent("organicshops: 54321");
    });
  });
});
