import { NextRequest, NextResponse } from "next/server";
import { contentSecurityPolicyHeaderValue, nonce } from "./utils/middleware/utils";

// can't filter prefetch request now...
// https://github.com/vercel/next.js/discussions/37736#discussioncomment-7274460
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - healthcheck
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico, sitemap.xml, robots.txt (metadata files)
     */
    {
      source:
        "/((?!poll/healthcheck|assets|_next/static|_next/image|favicon.ico|icon.ico|sitemap.xml|robots.txt).*)",
      missing: [
        { type: "header", key: "next-router-prefetch" },
        { type: "header", key: "purpose", value: "prefetch" },
      ],
    },
  ],
};

const dotFilePattern =
  /\.(env|config\.php|settings\.json|error\.log|access\.log|bak|old|zip|tmp|~|git|svn|sqlite|sql|class|jar|war)$/;

export default function middleware(request: NextRequest) {
  const requestHeaders = new Headers(request.headers);

  // add every request's log on server side
  console.log(new Date(), "req_method:", request.method, "| req_url:", request.url);

  if (dotFilePattern.test(request.url)) {
    // 如果 URL 是以 .xxx 結尾，redirect to 404 page
    return NextResponse.redirect(new URL("/404", request.url));
  }

  requestHeaders.set("x-nonce", nonce);
  requestHeaders.set("Content-Security-Policy", contentSecurityPolicyHeaderValue());

  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });

  response.headers.set("Content-Security-Policy", contentSecurityPolicyHeaderValue());

  return response;
}
