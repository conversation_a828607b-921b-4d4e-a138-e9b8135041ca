{"name": "uniopen-poll", "version": "0.1.0", "private": true, "sideEffects": false, "scripts": {"dev": "next dev", "dev:https": "next dev -p 443 --experimental-https", "build": "NODE_OPTIONS=\"--max-old-space-size=4096\" next build", "login:artifact": "aws codeartifact login --tool npm --repository frontend --domain uniopen --domain-owner 471112919450 --region ap-northeast-1 && echo \"registry=https://registry.npmjs.org/\" >> ~/.npmrc  && npm config set \"@pcsc:registry\" https://uniopen-471112919450.d.codeartifact.ap-northeast-1.amazonaws.com/npm/frontend/", "start:local": "cp .env.local .next/standalone/apps/uniopen-poll/.env && cp -r .next/static .next/standalone/apps/uniopen-poll/.next/. && node .next/standalone/apps/uniopen-poll/server.js --keepAliveTimeout 65000", "lint": "eslint . --quiet", "test": "TZ=Asia/Taipei jest", "test:coverage": "jest --coverageReporters='text-summary'", "prettier:check": "prettier --check \"**/*.+(tsx|ts)\"", "install:ci": "pnpm i --frozen-lockfile"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@ant-design/nextjs-registry": "^1.1.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@aws-sdk/client-appconfigdata": "^3.758.0", "@next/third-parties": "15.3.2", "@pcsc/antd-ui-components": "workspace:*", "@pcsc/apps-components": "workspace:*", "@pcsc/gtm-event": "workspace:*", "@pcsc/hooks": "workspace:*", "@pcsc/icons": "workspace:*", "@pcsc/store-providers": "workspace:*", "@pcsc/ui-components": "workspace:*", "@pcsc/utils": "workspace:*", "@sentry/browser": "8.55.0", "@sentry/nextjs": "^8.55.0", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "agentkeepalive": "^4.5.0", "antd": "^5.24.8", "axios": "^1.11.0", "bignumber.js": "^9.1.2", "crypto-js": "^4.2.0", "date-fns": "^3.3.1", "date-fns-tz": "^3.1.3", "framer-motion": "^12.16.0", "immer": "^10.1.1", "next": "15.3.2", "qs": "^6.14.0", "radash": "^12.1.0", "react": "19.1.0", "react-device-detect": "^2.2.3", "react-dom": "19.1.0", "react-share": "^5.2.2", "react-turnstile": "^1.1.4", "recharts": "^2.12.2", "sharp": "^0.34.2", "tailwind-merge": "^2.5.4", "usehooks-ts": "^3.1.1", "uuid": "^10.0.0", "zustand": "^5.0.3"}, "devDependencies": {"@pcsc/eslint-config": "workspace:*", "@pcsc/tailwind-config": "workspace:*", "@pcsc/typescript-config": "workspace:*", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/leaflet": "^1.9.14", "@types/node": "^20.17.6", "@types/react": "19.1.0", "@types/react-dom": "19.1.0", "autoprefixer": "^10.4.18", "cssnano": "^7.0.2", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.5", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}