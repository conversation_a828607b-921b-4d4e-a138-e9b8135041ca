{"name": "uniopen-notify", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start:local": "cp .env.local .next/standalone/apps/uniopen-notify/.env && cp -r public .next/standalone/apps/uniopen-notify/ && cp -r .next/static .next/standalone/apps/uniopen-notify/.next/. && node .next/standalone/apps/uniopen-notify/server.js --keepAliveTimeout 65000", "lint": "eslint . --quiet", "install:ci": "pnpm i --frozen-lockfile", "prettier:check": "prettier --check \"**/*.+(tsx|ts)\"", "check-types": "tsc --noEmit", "deploy": "pnpm run build && aws s3 rm s3://notify.dev.uniopen.com --recursive && aws s3 sync ./out s3://notify.dev.uniopen.com --cache-control \"must-revalidate, max-age=0, public\" --region ap-northeast-1"}, "dependencies": {"@next/third-parties": "15.3.2", "@pcsc/apps-components": "workspace:*", "@pcsc/gtm-event": "workspace:*", "@pcsc/hooks": "workspace:*", "@pcsc/icons": "workspace:*", "@pcsc/store-providers": "workspace:*", "@pcsc/ui-components": "workspace:*", "@pcsc/utils": "workspace:*", "@svgr/webpack": "^8.1.0", "next": "15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.1", "@pcsc/eslint-config": "workspace:*", "@pcsc/tailwind-config": "workspace:*", "@pcsc/typescript-config": "workspace:*", "@types/node": "^22.12.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "autoprefixer": "^10.4.20", "eslint": "^9.20.0", "postcss": "^8.5.5", "tailwindcss": "^3.4.17", "typescript": "5.7.3"}}