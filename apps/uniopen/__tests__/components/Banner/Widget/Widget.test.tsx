import React, { ReactNode } from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { WithAllProviders } from "../../../testHelpers/customRender";
import Widget from "@/components/Banner/Widget";

jest.mock("../../../../src/components/WeatherWidget", () => ({
  WeatherLargeWidget: jest.fn(() => <div>Mock WeatherLargeWidget</div>),
  WeatherSmallWidget: jest.fn(() => <div>Mock WeatherSmallWidget</div>),
}));

jest.mock("@pcsc/ui-components", () => {
  const MockScrollableContainer = ({ children }: { children: ReactNode }) => <div>{children}</div>;
  return {
    ContentSwiper: MockScrollableContainer,
  };
});

jest.mock("../../../../src/components/Banner/HomeWidgetDisplay", () =>
  jest.fn(() => <div>Mock HomeWidgetDisplay</div>),
);

jest.mock("@pcsc/icons/others", () => ({
  LoadingBgTransparent: jest.fn(() => <div>Mock LoadingBgTransparent</div>),
}));

jest.mock("../../../../src/components/Banner/Widget/ZodiacCard", () =>
  jest.fn(() => <div>Mock ZodiacCard</div>),
);
jest.mock("../../../../src/components/Banner/Widget/ZodiacCardLarge", () =>
  jest.fn(() => <div>Mock ZodiacCardLarge</div>),
);
jest.mock("../../../../src/components/Banner/Widget/NotifyCard", () =>
  jest.fn(() => <div>Mock NotifyCard</div>),
);
jest.mock("../../../../src/components/Banner/Widget/QuestionCard", () =>
  jest.fn(() => <div>Mock QuestionCard</div>),
);
jest.mock("../../../../src/components/Banner/Widget/OpenPointCard", () =>
  jest.fn(() => <div>Mock OpenPointCard</div>),
);

const mockData = {
  announcements: [
    {
      id: "1",
      title: "Test Announcement",
      content: "Test Content",
      startDateTime: 0,
      endDateTime: 0,
      updatedTs: 0,
      url: "https://test.com",
    },
  ],
};

describe("Widget", () => {
  it("renders the component with announcement data", () => {
    render(
      <WithAllProviders>
        <Widget />
      </WithAllProviders>,
    );
    expect(screen.getByText("Mock HomeWidgetDisplay")).toBeInTheDocument();
  });
});
