import MonthSwitcher from "@/app/memberCenter/myPoint/_components/MonthSwitcher";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";

import { format, subMonths } from "date-fns";
import "@testing-library/jest-dom";

// mock monthChange
const mockMonthChange = jest.fn();

describe("MonthSwitcher", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the current month correctly", () => {
    const currentMonth = new Date();
    render(<MonthSwitcher monthChange={mockMonthChange} />);

    const displayedMonth = format(currentMonth, "M");
    expect(screen.getByText(`${displayedMonth}月`)).toBeInTheDocument();
  });

  it("calls monthChange with the correct month when the 'Next' button is clicked, but not beyond current month", async () => {
    const mockMonthChange = jest.fn();
    const currentMonth = new Date();

    render(<MonthSwitcher monthChange={mockMonthChange} />);

    // init Month
    expect(screen.getByText(`${format(currentMonth, "M")}月`)).toBeInTheDocument();

    // click 'Next'
    fireEvent.click(screen.getByTestId("next-month"));

    // init 'Next' disable
    expect(screen.getByTestId("next-month")).toHaveClass("cursor-not-allowed");

    // check call monthChange
    await waitFor(() => {
      expect(mockMonthChange).toHaveBeenCalledWith(format(currentMonth, "M"));
    });
  });

  it("calls monthChange with the correct month when the 'Previous' button is clicked", async () => {
    const currentMonth = new Date();
    const previousMonth = subMonths(currentMonth, 1);

    render(<MonthSwitcher monthChange={mockMonthChange} />);

    // init
    expect(screen.getByText(`${format(currentMonth, "M")}月`)).toBeInTheDocument();
    // click previous
    fireEvent.click(screen.getByTestId("previous-month"));
    await waitFor(() => {
      expect(mockMonthChange).toHaveBeenCalledWith(format(previousMonth, "M"));
      expect(screen.getByText(`${format(previousMonth, "M")}月`)).toBeInTheDocument();
    });
  });

  it("does not change month when 'Next' button is clicked at max month offset", () => {
    render(<MonthSwitcher monthChange={mockMonthChange} />);

    fireEvent.click(screen.getByTestId("next-month"));
    expect(mockMonthChange).toHaveBeenCalledTimes(1); // Should not change the month
  });

  it("disables 'Previous' button when at min month offset (two months back)", async () => {
    const mockMonthChange = jest.fn();
    const currentMonth = new Date();

    render(<MonthSwitcher monthChange={mockMonthChange} />);

    // init
    expect(screen.getByText(`${format(currentMonth, "M")}月`)).toBeInTheDocument();

    // double click 'Previous'
    fireEvent.click(screen.getByTestId("previous-month"));
    fireEvent.click(screen.getByTestId("previous-month"));

    // 'Previous' disable
    expect(screen.getByTestId("previous-month")).toHaveClass("cursor-not-allowed");

    // check monthChange
    await waitFor(() => {
      expect(mockMonthChange).toHaveBeenCalledTimes(3); // 初始 + 2 次
    });
  });

  it("calls monthChange with the correct month when 'Previous' button is clicked", async () => {
    const mockMonthChange = jest.fn();
    const currentMonth = new Date();

    render(<MonthSwitcher monthChange={mockMonthChange} />);

    // init
    expect(screen.getByText(`${format(currentMonth, "M")}月`)).toBeInTheDocument();

    // click 'Previous'
    fireEvent.click(screen.getByTestId("previous-month"));

    // check monthChange & currentMonth
    await waitFor(() => {
      expect(mockMonthChange).toHaveBeenCalledWith(format(subMonths(currentMonth, 1), "M"));
    });
  });
});
