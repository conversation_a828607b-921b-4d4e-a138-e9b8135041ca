import MyCollect from "@/app/memberCenter/myCollect/page";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useRouter, useSearchParams } from "next/navigation";

// Mock the required modules
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

jest.mock("@/app/memberCenter/myCollect/_components/ShowSelect", () =>
  jest.fn(() => <div>ShowSelect Component</div>),
);
jest.mock("@/app/memberCenter/myCollect/_components/ShowAll", () =>
  jest.fn(() => <div>ShowAll Component</div>),
);

jest.mock("@/components/ScrollableTabs", () => {
  const MockTabs = ({
    tabList,
    tabChange,
  }: {
    tabList: { id: string; title: string }[];
    tabChange: CallableFunction;
  }) => {
    return (
      <div>
        {tabList.map((tab) => {
          return (
            <div
              key={`tab-${tab.id}`}
              onClick={() => {
                tabChange(tab);
              }}
            >
              {tab.title}
            </div>
          );
        })}
      </div>
    );
  };
  return MockTabs;
});

describe("MyCollect Component", () => {
  let mockReplace: jest.Mock;

  beforeEach(() => {
    // Reset mocks before each test
    mockReplace = jest.fn();

    // Setup the mock for useRouter
    (useRouter as jest.Mock).mockImplementation(() => ({
      replace: mockReplace,
    }));

    // Mock useSearchParams to return different `collectType` based on the test case
  });

  it("renders correctly with the default tab", () => {
    // Mocking useSearchParams to simulate no `collectType` parameter
    (useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn().mockReturnValue(null), // No collectType param
    });

    render(<MyCollect />);

    // Check if the correct components are rendered
    expect(screen.getByText("我的收藏")).toBeInTheDocument();
    // Check for the presence of tab titles rendered by CollapsibleTabs
    expect(screen.getByText("全部")).toBeInTheDocument();
    expect(screen.getByText("優惠")).toBeInTheDocument();
    expect(screen.getByText("商品")).toBeInTheDocument();
    expect(screen.getByText("文章")).toBeInTheDocument();
    // Check if ShowAll is rendered by default
    expect(screen.getByText("ShowAll Component")).toBeInTheDocument();
  });

  it("renders ShowSelect when a tab is selected", async () => {
    // Mock useSearchParams to simulate the `collectType` being passed in as "sale"
    (useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn().mockReturnValue("sale"), // collectType = "sale"
    });

    render(<MyCollect />);

    // Ensure ShowSelect component is rendered based on the selected tab
    await waitFor(() => {
      expect(screen.getByText("ShowSelect Component")).toBeInTheDocument();
    });

    // const selectedTab = screen.getByText("優惠");
    // expect(selectedTab).toHaveClass("bg-ct/button-segmented/main/selected");
  });

  it("updates the tab correctly and renders the corresponding component", async () => {
    (useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn().mockReturnValue("product"), // No collectType param
    });
    render(<MyCollect />);

    // Simulate changing the tab to 'product'
    fireEvent.click(screen.getByText("商品"));

    // Verify that the ShowSelect component is displayed after changing tabs
    expect(screen.getByText("ShowSelect Component")).toBeInTheDocument();
    expect(screen.queryByText("ShowAll Component")).not.toBeInTheDocument();
  });

  it("calls router.replace when a tab is selected from the URL query parameter", async () => {
    // Mock useSearchParams to simulate the `collectType` being passed in as "news"
    (useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn().mockReturnValue("news"), // collectType = "news"
    });

    render(<MyCollect />);

    // Verify that router.replace was called to update the URL
    expect(mockReplace).toHaveBeenCalledWith("/memberCenter/myCollect");

    // Check if the ShowSelect component is rendered based on the selected tab
    expect(screen.getByText("ShowSelect Component")).toBeInTheDocument();
    // const selectedTab = screen.getByText("文章");
    // expect(selectedTab).toHaveClass("bg-ct/button-segmented/main/selected");
  });
});
