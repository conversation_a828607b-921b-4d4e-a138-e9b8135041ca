import "@testing-library/jest-dom";
import MemberCenterNoResultError from "@/app/memberCenter/_components/MemberCenterNoResultError";
import { render, screen, fireEvent } from "@testing-library/react";

// Mock NoSearchResults 圖示
jest.mock("@pcsc/icons/empty", () => ({
  NoSearchResults: ({ width, height }: { width: number; height: number }) => (
    <svg data-testid="no-search-results" width={width} height={height}>
      <circle cx="50" cy="50" r="50" fill="gray" />
    </svg>
  ),
}));

describe("MemberCenterNoResultError", () => {
  const mockClickHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders the error state correctly", () => {
    render(<MemberCenterNoResultError clickHandler={mockClickHandler} />);

    // Check if the NoSearchResults icon is rendered
    const icon = screen.getByTestId("no-search-results");
    expect(icon).toBeInTheDocument();

    // Check if the message is rendered
    const message = screen.getByText("系統忙碌中，請稍後再試");
    expect(message).toBeInTheDocument();

    // Check if the button is rendered with the correct text
    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent("重新取得");
  });

  test("clicking the button calls the clickHandler", () => {
    render(<MemberCenterNoResultError clickHandler={mockClickHandler} />);

    // Find the button and simulate a click
    const button = screen.getByRole("button");
    fireEvent.click(button);

    // Check if the clickHandler was called
    expect(mockClickHandler).toHaveBeenCalledTimes(1);
  });
});
