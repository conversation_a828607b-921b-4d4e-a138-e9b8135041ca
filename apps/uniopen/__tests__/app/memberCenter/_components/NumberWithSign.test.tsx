import "@testing-library/jest-dom";
import NumberWithSign from "@/app/memberCenter/_components/NumberWithSign";
import { render, screen, waitFor } from "@testing-library/react";

// Mock format function for testing
const mockFormatFunc = (value: string) => `$${value}`;

describe("NumberWithSign", () => {
  test("renders positive value with + sign", () => {
    render(<NumberWithSign value={123} />);

    const signElement = screen.getByText("+");
    const valueElement = screen.getByText("123");
    expect(signElement).toBeInTheDocument();
    expect(valueElement).toBeInTheDocument();

    const parentElement = valueElement.closest("div");
    expect(parentElement).toHaveClass("text-ct/text/success/subtle");
  });

  test("renders negative value with - sign", () => {
    render(<NumberWithSign value={-123} />);

    const signElement = screen.getByText("-");
    const valueElement = screen.getByText("123");

    expect(signElement).toBeInTheDocument();
    expect(valueElement).toBeInTheDocument();

    const parentElement = valueElement.closest("div");
    expect(parentElement).toHaveClass("text-ct/text/danger/subtle");
  });

  test("renders zero value with + sign", () => {
    render(<NumberWithSign value={0} />);

    const signElement = screen.getByText("+");
    const valueElement = screen.getByText("0");

    expect(signElement).toBeInTheDocument();
    expect(valueElement).toBeInTheDocument();

    const parentElement = valueElement.closest("div");
    expect(parentElement).toHaveClass("text-ct/text/success/subtle");
  });

  test("renders value with format function applied", () => {
    render(<NumberWithSign value={123} formatFunc={mockFormatFunc} />);

    const valueElement = screen.getByText("$123");

    expect(valueElement).toBeInTheDocument();
  });
});
