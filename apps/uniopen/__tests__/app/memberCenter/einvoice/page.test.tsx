import "@testing-library/jest-dom";
import EinvoicePage from "@/app/memberCenter/einvoice/page";
import { render, screen } from "@testing-library/react";

jest.mock("@/app/memberCenter/_components/pageTitle", () => ({
  __esModule: true,
  default: jest.fn(() => <div>發票日誌</div>),
}));

jest.mock("@/app/memberCenter/einvoice/PageContainer", () => ({
  __esModule: true,
  default: jest.fn(() => <div>發票日誌內容</div>),
}));

describe("EinvoicePage", () => {
  it("should render PageTitle and PageContainer", () => {
    render(<EinvoicePage />);

    expect(screen.getByText("發票日誌")).toBeInTheDocument();
    expect(screen.getByText("發票日誌內容")).toBeInTheDocument();
  });
});
