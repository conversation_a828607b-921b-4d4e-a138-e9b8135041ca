"use client";
import React from "react";
import { useParams } from "next/navigation";
import { HashTag, Tag } from "@pcsc/ui-components";
import Link from "next/link";
import { isNumber } from "radash";
import { sendGTMClick } from "@pcsc/utils";
import PollDetailLayout from "@/components/Layouts/PollDetailLayout";
import { useQueryQuizBasicInfo } from "../../_hooks/useQuery";
import { isPublishedPoll } from "@/app/(basicLayout)/poll/utils";
import ImageWithTag from "@/components/PollOrQuiz/ImageWithTag";
import { DateRange, VotedAmount } from "@/components/PollOrQuiz/common";
import QuizGiveawayCard from "../../_components/QuizGiveawayCard";

const QuizDetailVotedAmount: React.FC = () => {
  const id = useParams().id as string;
  const { data } = useQueryQuizBasicInfo({ id });
  const { vote_num: votedAmount, status } = data! || {};
  const hasVotedAmount = isNumber(votedAmount);
  if (isPublishedPoll({ status }) || !hasVotedAmount) {
    return null;
  }

  return <VotedAmount className="text-Body.regular_16" amount={votedAmount} />;
};

const QuizStatus = () => {
  return (
    <div className="mt-4">
      <Tag type="moderate_general">
        挑戰成功即可抽獎：限量<span className="font-poppins">1500</span>
      </Tag>
    </div>
  );
};

const QuizDetails = () => {
  const id = useParams().id as string;
  const { data } = useQueryQuizBasicInfo({ id });

  const { title, image_url, description, hashtag, startTs, endTs, giveaway } = data || {};
  const hasHashTag = hashtag?.length;

  return (
    <PollDetailLayout
      title={title}
      statusComponent={<QuizStatus />}
      imgComponent={
        <>
          <ImageWithTag
            className="max-s:[&_>_div]:rounded-b-none max-s:[&_>_div]:rounded-t-xl"
            src={image_url}
            alt={title}
            loading="eager"
          />
          {giveaway ? <QuizGiveawayCard point={Number(giveaway.point).toFixed(0)} /> : null}
        </>
      }
      bottomComponent={
        <>
          <div className="mb-3 mt-6 flex flex-col gap-1">
            <p className="flex flex-col gap-1 text-Body.regular_16 text-ct/text/main/subtle s:flex-row">
              挑戰時段：
              {startTs && endTs ? <DateRange startDate={startTs} endDate={endTs} /> : null}
            </p>
            <QuizDetailVotedAmount />
          </div>
          <p className="text-Body.regular_16 text-ct/text/main/subtle s:text-Body.regular_18">
            {description}
          </p>
          {hasHashTag ? (
            <ul className="mt-6 flex flex-wrap gap-2 s:mt-8">
              {hashtag.map((name) => (
                <li key={`quiz-tag-${name}`}>
                  <Link
                    href={`/quiz/hashtag/${name}`}
                    onClick={() => {
                      sendGTMClick(`quiz_tag_${name}`);
                    }}
                  >
                    <HashTag as="h2">{name}</HashTag>
                  </Link>
                </li>
              ))}
            </ul>
          ) : undefined}
        </>
      }
    />
  );
};

export default QuizDetails;
