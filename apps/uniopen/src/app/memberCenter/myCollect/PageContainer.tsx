"use client";
import React, { useEffect, useState } from "react";
import ShowSelect from "@/app/memberCenter/myCollect/_components/ShowSelect";
import ShowAll from "@/app/memberCenter/myCollect/_components/ShowAll";
import { CollectionEnum, type TabItemType } from "@pcsc/api";
import { useRouter, useSearchParams } from "next/navigation";
import ScrollableTabs from "@/components/ScrollableTabs";

const tabList = [
  { title: "全部", id: "all" },
  { title: "優惠", id: "sale" },
  { title: "商品", id: "product" },
  { title: "文章", id: "news" },
];

const PageContainer: React.FC = () => {
  const router = useRouter();
  const collectType = useSearchParams().get("collectType");
  const [current, setCurrent] = useState<TabItemType>(tabList[0]);
  const handleTabChange = (tab: TabItemType) => {
    setCurrent(tab);
  };

  const updateTab = (update: string) => {
    const currentTab = tabList.filter((tab) => tab.id === update);
    handleTabChange(currentTab[0]);
  };

  useEffect(() => {
    const type = tabList.filter((tab) => tab.id === collectType)[0];
    if (type) {
      router.replace("/memberCenter/myCollect");
      setCurrent(type);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <ScrollableTabs
        active={current}
        tabList={tabList}
        tabChange={handleTabChange}
        className="mb-9 mt-4 xs:mt-6"
      />
      {current.id === "all" ? (
        <ShowAll updateTab={updateTab} />
      ) : (
        <ShowSelect active={CollectionEnum[current.id as keyof typeof CollectionEnum]} />
      )}
    </>
  );
};

export default PageContainer;
