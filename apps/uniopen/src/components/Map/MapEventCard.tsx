import { type MapCampaignListItem } from "@pcsc/api";
import { Tag } from "@pcsc/ui-components";
import { sendGTMClick } from "@pcsc/utils";
import { format, isValid } from "date-fns";
import Link from "next/link";
import CardBasic from "../Home/MapSection/CardBasic";

export type MapEventCardProps = {
  event: MapCampaignListItem;
  className?: string;
};

const MapEventCard = ({ event, className = "" }: MapEventCardProps) => {
  const { banner, title, startTs, tag, specialTag } = event;
  const startDate =
    startTs && isValid(new Date(startTs)) ? format(new Date(startTs), "yyyy/MM/dd") : undefined;

  return (
    <Link
      href={`/map/events/${event.urlSuffix}`}
      onClick={() => {
        sendGTMClick(`map/${title}`);
      }}
    >
      <CardBasic
        isLoading={!event}
        image={banner}
        specialTag={specialTag}
        className={className}
        share={{ link: `/map/events/${event.urlSuffix}`, title: title }}
      >
        <section className="flex h-[88px] flex-col justify-between">
          <h2 className="line-clamp-2 text-Title.medium text-ct/text/main/general">{title}</h2>
          <div className="flex items-center justify-between gap-2">
            <div className="flex min-w-0 items-center justify-center gap-2">
              {startDate && (
                <Tag type="main_subtle" size="M">
                  {startDate} 開始
                </Tag>
              )}
              {tag && (
                <Tag type="dim_subtle" size="M" className="max-w-full truncate xs:hidden s:block">
                  {tag}
                </Tag>
              )}
            </div>
            {/* TODO: Phase2 要加上收藏功能 */}
            {/* <FavoriteIcon iconType=w"dim-neutral" iconSize="w-6 h-6" /> */}
          </div>
        </section>
      </CardBasic>
    </Link>
  );
};

export default MapEventCard;
