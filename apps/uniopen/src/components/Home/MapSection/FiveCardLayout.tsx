import { type MapCampaignListItem } from "@pcsc/api";
import MapEventCard from "@/components/Map/MapEventCard";
import MapEventCardSmall from "@/components/Map/MapEventCardSmall";

const FiveCardLayout = ({ eventList }: { eventList: MapCampaignListItem[] }) => {
  return (
    <div className="flex grid-cols-2 grid-rows-4 flex-col gap-3 xs:gap-5 s:grid lg:grid-cols-3 lg:grid-rows-2">
      <div className="col-span-1 row-span-2">
        <MapEventCard event={eventList[0]} />
      </div>

      <div className="col-span-1 row-span-2 block lg:hidden">
        <MapEventCard event={eventList[1]} />
      </div>
      <div className="col-span-1 row-span-1 hidden lg:block">
        <MapEventCardSmall event={eventList[1]} />
      </div>

      <div className="col-span-1 row-span-2 block lg:hidden">
        <MapEventCard event={eventList[2]} />
      </div>
      <div className="col-span-1 row-span-1 hidden lg:block">
        <MapEventCardSmall event={eventList[2]} />
      </div>

      <div className="col-span-1 row-span-1">
        <MapEventCardSmall event={eventList[3]} />
      </div>
      <div className="col-span-1 row-span-1">
        <MapEventCardSmall event={eventList[4]} />
      </div>
    </div>
  );
};

export default FiveCardLayout;
