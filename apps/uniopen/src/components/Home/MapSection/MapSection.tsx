import { useQuery } from "@tanstack/react-query";
import SectionContainer from "../../SectionContainer";
import { getMapCampaignList } from "@pcsc/api";
import ErrorPlaceholder from "../../ErrorPlaceholder";
import Link from "next/link";
import { Button } from "@pcsc/ui-components";
import ThreeCardLayout from "./ThreeCardLayout";
import FourCardLayout from "./FourCardLayout";
import FiveCardLayout from "./FiveCardLayout";
import SixCardLayout from "./SixCardLayout";
import { type MapCampaignListItem } from "@pcsc/api";

const renderLayoutByCount = (storeCampaigns: MapCampaignListItem[]) => {
  if (storeCampaigns.length === 3) return <ThreeCardLayout eventList={storeCampaigns} />;
  if (storeCampaigns.length === 4) return <FourCardLayout eventList={storeCampaigns} />;
  if (storeCampaigns.length === 5) return <FiveCardLayout eventList={storeCampaigns} />;
  if (storeCampaigns.length === 6) return <SixCardLayout eventList={storeCampaigns} />;
};

const MapSection = () => {
  const { data, isFetching, isError } = useQuery({
    queryKey: ["mapCampaignList", "homepage"],
    queryFn: () =>
      getMapCampaignList({
        offset: 0,
        limit: 6,
        sort: "ASC",
        order: "sort",
      }),
    select: (res) => ({
      ...res,
      storeCampaigns: res.storeCampaigns.map((item) => ({ ...item, specialTag: undefined })),
    }),
  });
  return (
    <>
      {!data?.storeCampaigns || data?.storeCampaigns?.length < 3 ? null : (
        <SectionContainer title="主題地圖">
          <div>
            {isFetching ? (
              <></>
            ) : isError ? (
              <ErrorPlaceholder />
            ) : (
              renderLayoutByCount(data.storeCampaigns)
            )}
            <div className="mt-8 flex w-full justify-end">
              <Link
                href="/map"
                className="flex-shrink-0 text-Title.medium text-ct/text/main/general"
              >
                <Button variant="button-stroke" size="m" className="flex items-center">
                  看更多
                </Button>
              </Link>
            </div>
          </div>
        </SectionContainer>
      )}
    </>
  );
};

export default MapSection;
