import { type MapCampaignListItem } from "@pcsc/api";
import MapEventCard from "@/components/Map/MapEventCard";
import MapEventCardSmall from "@/components/Map/MapEventCardSmall";

const SixCardLayout = ({ eventList }: { eventList: MapCampaignListItem[] }) => {
  return (
    <div className="flex grid-cols-2 flex-col gap-3 xs:gap-5 s:grid lg:grid-cols-3">
      <div className="col-span-1">
        <MapEventCard event={eventList[0]} />
      </div>
      <div className="col-span-1">
        <MapEventCard event={eventList[1]} />
      </div>

      <div className="col-span-1 block s:max-lg:hidden">
        <MapEventCard event={eventList[2]} />
      </div>
      <div className="col-span-1 hidden s:max-lg:block">
        <MapEventCardSmall event={eventList[2]} />
      </div>

      <div className="col-span-1">
        <MapEventCardSmall event={eventList[3]} />
      </div>
      <div className="col-span-1">
        <MapEventCardSmall event={eventList[4]} />
      </div>
      <div className="col-span-1">
        <MapEventCardSmall event={eventList[5]} />
      </div>
    </div>
  );
};

export default SixCardLayout;
