import { type MapCampaignListItem } from "@pcsc/api";
import MapEventCard from "@/components/Map/MapEventCard";
import MapEventCardSmall from "@/components/Map/MapEventCardSmall";

const ThreeCardLayout = ({ eventList }: { eventList: MapCampaignListItem[] }) => {
  return (
    <div className="flex flex-col gap-3 xs:gap-5 s:grid s:grid-cols-2 s:grid-rows-2 lg:grid-cols-3 lg:grid-rows-1">
      <div className="col-span-1 row-span-2 lg:row-span-1">
        <MapEventCard event={eventList[0]} />
      </div>
      <div className="block s:max-lg:hidden">
        <MapEventCard event={eventList[1]} />
      </div>
      <div className="block s:max-lg:hidden">
        <MapEventCard event={eventList[2]} />
      </div>
      <div className="row-span-2 hidden gap-5 s:max-lg:grid">
        <div className="row-span-1">
          <MapEventCardSmall event={eventList[1]} />
        </div>
        <div className="row-span-1">
          <MapEventCardSmall event={eventList[2]} />
        </div>
      </div>
    </div>
  );
};

export default ThreeCardLayout;
