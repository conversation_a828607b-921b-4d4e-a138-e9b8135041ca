// TODO: 之後時間會變 optional
export type MapCampaignInfoType = {
  title: string;
  desc: string;
  banner: string;
  startTs: number;
  endTs: number;
  storeTypeList: string[];
  mainImages: string[];
  otherImages: string[];
  stores: {
    type: string;
    num: number;
  }[];
};

export type MapCampaignListItem = {
  id: string;
  title: string;
  urlSuffix: string;
  desc: string | null;
  banner: string;
  startTs: number | null;
  endTs: number | null;
  category: string | null;
  tag: string | null;
  specialTag: string | null;
  sort: number | null;
};

export type MapCampaignListResponse = {
  storeCampaigns: MapCampaignListItem[];
  total: number;
};

export type MapStoreInfoType = {
  id: string;
  name: string;
  address: string;
  telNo: string;
  loc: [number, number];
  specialTags: string[];
};

export type MapStoreInfoInSearchPageType = MapStoreInfoType & { services: number[] };

export type StoreCampaignInfoType = {
  urlSuffix: string;
  title: string;
  desc: string;
  banner: string;
  startTs: number;
  endTs: number;
  storeTypeList: string[];
};

export type MapStoreDetailInfoType = {
  id: string;
  name: string;
  zip: string;
  address: {
    city: string;
    district: string;
    address: string;
  };
  areaNo: string;
  tel: string;
  location: {
    coordinates: [number, number];
    type: string;
  };
  type: string;
  createdTs: string;
  updatedTs: string;
  bannerUrl: string | null;
  specialTags: string[];
  services: MapStoreServiceType[];
  campaignList?: StoreCampaignInfoType[] | null;
};

export type MapStoreServiceType = {
  id: number;
  name: string;
  details: MapStoreServiceDetailType[];
};

export type MapStoreServiceDetailType = {
  id: number;
  name: string;
  count: number;
};

export const enum DrawerType {
  Event = "event",
  Search = "search",
  Store = "store",
}
