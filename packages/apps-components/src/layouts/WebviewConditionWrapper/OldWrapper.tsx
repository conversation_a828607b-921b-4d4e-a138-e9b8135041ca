"use client";
import { ReactNode, useEffect, useState } from "react";
import { useSessionStorage } from "usehooks-ts";
import { useIsWormHoleMode } from "./IsWormHoleModeProvider";
import { Footer, UniopenHeader } from "../..";
import dynamic from "next/dynamic";
import { cn } from "@pcsc/tailwind-config/utils";

const DynamicGenerateFloatingWindow = dynamic(() => import("./GenerateFloatingWindowInfo"), {
  ssr: false,
});

const WebviewConditionWrapper = ({
  children,
  enableFloatingWindow = false,
}: {
  children: ReactNode;
  enableFloatingWindow: boolean;
}) => {
  const [hasMounted, setHasMounted] = useState(false);
  const { isWormHoleMode } = useIsWormHoleMode();
  const [wormhole] = useSessionStorage("wormhole", isWormHoleMode);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return null; // Avoid hydration mismatch
  }

  return (
    <>
      {wormhole ? null : <UniopenHeader />}
      {children}
      {enableFloatingWindow && !wormhole ? <DynamicGenerateFloatingWindow /> : null}
      <Footer className={cn(wormhole && "hidden")} />
    </>
  );
};
export default WebviewConditionWrapper;
