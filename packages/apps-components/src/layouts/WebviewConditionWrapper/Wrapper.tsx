"use client";
import { type ReactNode } from "react";
import { useIsWormHoleMode } from "./IsWormHoleModeProvider";
import { Footer, UniopenHeader } from "../..";
import dynamic from "next/dynamic";
import { cn } from "@pcsc/tailwind-config/utils";

const DynamicGenerateFloatingWindow = dynamic(() => import("./GenerateFloatingWindowInfo"), {
  ssr: false,
});

const WebviewConditionWrapper = ({
  children,
  enableFloatingWindow = false,
}: {
  children: ReactNode;
  enableFloatingWindow: boolean;
}) => {
  const { isWormHoleMode } = useIsWormHoleMode();

  return (
    <>
      {isWormHoleMode ? null : <UniopenHeader />}
      {children}
      {enableFloatingWindow && !isWormHoleMode ? <DynamicGenerateFloatingWindow /> : null}
      <Footer className={cn(isWormHoleMode && "hidden")} />
    </>
  );
};
export default WebviewConditionWrapper;
