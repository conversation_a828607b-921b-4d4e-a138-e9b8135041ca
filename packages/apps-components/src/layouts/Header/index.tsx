"use client";
import React, { useCallback, useState } from "react";
import { Logo } from "@pcsc/icons/others";
import dynamic from "next/dynamic";
import Link from "next/link";
import { useUserStore } from "@pcsc/store-providers";
import { useRouter } from "next/navigation";
import { PostLogin } from "@pcsc/api";
import { MobileOutline, Search } from "@pcsc/icons/feature";
import { appconfigEnv, env, sendGTMClick } from "@pcsc/utils";
import SpeechSearchTextField from "../../components/SpeechSearchTextField";
import Tabs from "./Tabs";
import HeaderAvatar from "./HeaderAvatar";
import NotificationsBell from "./NotificationsBell";
import WebMessagesBell from "./WebMessagesBell";
import { Drawer as CustomDrawer } from "../../components/Drawer";
import SearchModel from "../../components/SearchModel";
import { cn } from "@pcsc/tailwind-config/utils";
import { PositionedAppNavigateSmartBanner } from "./AppNavigateSmartBanner";
import { getShouldShowAppNavigateSmartBanner } from "./AppNavigateSmartBanner/utils";

import { usePathname } from "next/navigation";
import { GTMEventFactory } from "@pcsc/gtm-event";

export type HeaderTabItem = {
  key: string;
  label: React.ReactNode;
  activePaths: string[];
};

const DynamicSideBar = dynamic(() => import("./Sidebar"), {
  ssr: false,
});

const DownloadApp = () => (
  <Link href="/app" className="hidden items-center gap-1 min-[691px]:flex">
    <MobileOutline className="size-6" />
    <p className="text-nowrap text-Title.medium">下載 App</p>
  </Link>
);

const Header = ({ withTabs = true, className }: { withTabs?: boolean; className?: string }) => {
  const notifyEnabled = appconfigEnv("CONFIG_PUBLIC_NOTIFY_ENABLED");
  const router = useRouter();
  const { query, uniOpen, updateQuery } = useUserStore((state) => state);
  const isUser = uniOpen.isUser;
  const [vocalResult, setVocalResult] = useState(query ?? "");
  const [drawerInfo, setDrawerInfo] = useState<{
    type: "shopping" | "member" | "notifications";
    isShow: boolean;
  }>({ type: "shopping", isShow: false });
  const [isSearchBackDropShow, setIsSearchBackDropShow] = useState(false);

  const pathname = usePathname();

  const clickMemberLink = useCallback(
    async (e: { preventDefault: () => void }) => {
      e.preventDefault();
      if (isUser) {
        router.push("/memberCenter/myCollect");
      } else {
        await PostLogin(`${window.location.origin}/memberCenter/myCollect`);
      }
    },
    [isUser, router],
  );

  const shouldShowSmartBanner = getShouldShowAppNavigateSmartBanner(pathname);
  const showDownloadApp = appconfigEnv("CONFIG_PUBLIC_APP_ENTRY_ENABLED");

  return (
    <>
      {shouldShowSmartBanner && <PositionedAppNavigateSmartBanner urlPath={pathname} />}

      <div
        className={cn(
          "sticky top-0 z-50 flex h-[86px] w-full flex-col bg-at/neutral/light shadow-Top_Bar transition-all s:h-16 s:flex-row s:justify-center",
          className,
        )}
      >
        <div className="mx-4 flex h-[42px] items-center justify-between pb-[6px] pt-3 s:mx-5 s:h-full s:w-full s:pb-0 s:pt-0 2xl:w-[1380px]">
          <div className="flex items-center lg:w-[49.8%]">
            <Link href="/">
              <Logo className="mr-6 h-6 w-[128px] s:mr-6 s:h-8 s:w-[160px]" />
            </Link>
            <div className="hidden w-auto grow lg:block">
              {withTabs && (
                <SpeechSearchTextField
                  vocalResult={vocalResult}
                  setVocalResult={setVocalResult}
                  size="small"
                  customStyle="min-[601px]:!h-10 h-!10 [&_.search-input]:bg-transparent !bg-ct/input-search/dim/container shadow-[0px_0px_0px_0px_rgba(0,0,0,0.1)] [&_.search-btn]:!min-w-[44px]"
                />
              )}
            </div>
            {withTabs && (
              <div className="hidden s:block lg:hidden">
                <Tabs setDrawerInfo={setDrawerInfo} />
              </div>
            )}
          </div>
          {withTabs && (
            <div className="hidden px-8 lg:block">
              <Tabs setDrawerInfo={setDrawerInfo} />
            </div>
          )}
          <section
            className={cn(
              "flex w-[164px] flex-nowrap justify-end gap-4 s:gap-6 min-[691px]:w-[280px]",
            )}
          >
            {isUser === undefined ? null : isUser ? (
              <>
                {showDownloadApp && <DownloadApp />}
                <button
                  type="button"
                  onClick={() => {
                    setIsSearchBackDropShow(true);
                  }}
                  className="block outline-none lg:hidden"
                  id="collapseSearchIcon"
                >
                  <Search className="h-6 w-6 [&>path]:fill-ct/icon/main/subtle" />
                </button>
                {/* web push 的 message 只會出現在desktop版本中dev的小鈴噹，其他都是原來的通知 */}
                {notifyEnabled ? (
                  <WebMessagesBell setDrawerInfo={setDrawerInfo} />
                ) : (
                  <NotificationsBell setDrawerInfo={setDrawerInfo} />
                )}
                <HeaderAvatar setDrawerInfo={setDrawerInfo} />
              </>
            ) : (
              <>
                {showDownloadApp && <DownloadApp />}
                <button
                  type="button"
                  onClick={() => {
                    setIsSearchBackDropShow(true);
                  }}
                  className="block outline-none lg:hidden"
                  id="collapseSearchIcon"
                >
                  <Search className="h-6 w-6 [&>path]:fill-ct/icon/main/subtle" />
                </button>
                <button
                  type="button"
                  className="hidden text-nowrap text-Title.medium text-ct/text/main/general lg:block"
                  onClick={async (e) => {
                    sendGTMClick("menu_login");
                    GTMEventFactory.create("uniopen_click")
                      .setClickId("導覽列_會員中心")
                      .setClickContent("會員中心")
                      .setClickUrl("/memberCenter/myCollect");
                    await clickMemberLink(e);
                  }}
                >
                  會員中心
                </button>
                <button
                  type="button"
                  data-testid="header-login"
                  className="text-nowrap text-Title.medium text-ct/text/moderate/general"
                  onClick={async () => {
                    sendGTMClick("menu_login");
                    GTMEventFactory.create("uniopen_click")
                      .setClickId("導覽列_會員中心")
                      .setClickContent("會員中心")
                      .setClickUrl("/memberCenter/myCollect");
                    await PostLogin();
                  }}
                >
                  登入
                </button>
              </>
            )}
          </section>
          <CustomDrawer
            open={isSearchBackDropShow}
            onClose={() => {
              setIsSearchBackDropShow(false);
            }}
            placement={"bottom"}
            hasMask={true}
            hasCloseBtn={false}
            className={"[&_.drawer-content]:rounded-t-xl"}
          >
            <SearchModel
              updateQuery={updateQuery}
              searchModelInput={vocalResult}
              setSearchModelInput={setVocalResult}
              onClose={() => {
                setIsSearchBackDropShow(false);
              }}
              searchInputPlaceholder={"搜尋"}
              type="Global"
            />
          </CustomDrawer>
        </div>
        {withTabs && (
          <div className="mx-5 flex h-[44px] items-center justify-center s:hidden">
            <Tabs setDrawerInfo={setDrawerInfo} />
          </div>
        )}
        <DynamicSideBar
          opened={drawerInfo.isShow}
          drawerType={drawerInfo.type}
          onClose={() => {
            setDrawerInfo((prev) => ({ ...prev, isShow: false }));
          }}
        />
      </div>
    </>
  );
};
export default Header;
