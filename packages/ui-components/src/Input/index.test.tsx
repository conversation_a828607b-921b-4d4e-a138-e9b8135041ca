import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import {
  Input,
  InputFieldWrapper,
  InputFieldTitle,
  InputStatusMessage,
  InputTextArea,
  InputTextAreaStatusMessage,
  getIsValidateFailed,
  getValidateStatus,
  WordCounts,
} from "./index";

// Mock external dependencies
vi.mock("@pcsc/icons/feature", () => ({
  Info: ({ className }: any) => (
    <div data-testid="info-icon" className={className}>
      InfoIcon
    </div>
  ),
}));

describe("Input Component", () => {
  describe("Basic Rendering", () => {
    it("renders input with default props", () => {
      render(<Input />);

      const input = screen.getByRole("textbox");
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute("type", "text");
      expect(input).not.toBeDisabled();
      expect(input).not.toHaveAttribute("readonly");
    });

    it("renders with custom placeholder", () => {
      render(<Input placeholder="Enter your name" />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("placeholder", "Enter your name");
    });

    it("renders with custom className", () => {
      render(<Input className="custom-input" />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveClass("custom-input");
    });

    it("renders with data-slot attribute", () => {
      render(<Input />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("data-slot", "input");
    });
  });

  describe("Input Types", () => {
    it("renders with different input types", () => {
      const { rerender } = render(<Input type="email" />);
      expect(screen.getByRole("textbox")).toHaveAttribute("type", "email");

      rerender(<Input type="password" />);
      const passwordInput = screen.getByDisplayValue("");
      expect(passwordInput).toHaveAttribute("type", "password");

      rerender(<Input type="number" />);
      expect(screen.getByRole("spinbutton")).toHaveAttribute("type", "number");
    });
  });

  describe("Field Sizes", () => {
    it("applies small size classes", () => {
      render(<Input fieldSize="S" />);

      const container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("h-8", "px-2", "text-Body.regular_14");
    });

    it("applies medium size classes", () => {
      render(<Input fieldSize="M" />);

      const container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("h-9", "px-2", "text-Body.regular_16");
    });

    it("applies large size classes (default)", () => {
      render(<Input fieldSize="L" />);

      const container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("h-14", "px-4", "text-Body.regular_16");
    });
  });

  describe("Status and Validation", () => {
    it("renders default status without validation icons", () => {
      render(<Input status="default" />);

      expect(screen.queryByText("ValidateErrorIcon")).not.toBeInTheDocument();
      expect(screen.queryByText("ValidateCorrectIcon")).not.toBeInTheDocument();
    });

    it("renders error status with error icon", () => {
      render(<Input status="error" />);

      const container = screen.getByRole("textbox").parentElement;
      expect(container).toContainHTML('fill="#F11637"'); // Error icon color
    });

    it("renders success status with success icon", () => {
      render(<Input status="success" />);

      const container = screen.getByRole("textbox").parentElement;
      expect(container).toContainHTML('fill="#1BA279"'); // Success icon color
    });

    it("applies correct CSS classes for different statuses", () => {
      const { rerender } = render(<Input status="default" />);
      let container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("border-ct/input-general/main/stroke/default");

      rerender(<Input status="error" />);
      container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("border-ct/input-general/main/stroke/error");

      rerender(<Input status="success" />);
      container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("border-ct/input-general/main/stroke/success");
    });
  });

  describe("Color Variants", () => {
    it("applies main color classes (default)", () => {
      render(<Input color="main" />);

      const container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("border-ct/input-general/main/stroke/default");
    });

    it("applies dim color classes", () => {
      render(<Input color="dim" />);

      const container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("border-ct/input-general/dim/stroke/default");
    });
  });

  describe("Disabled State", () => {
    it("renders disabled input", () => {
      render(<Input disabled />);

      const input = screen.getByRole("textbox");
      expect(input).toBeDisabled();
      expect(input).toHaveClass("cursor-not-allowed", "text-ct/text/main/pale");
    });

    it("applies disabled container styles", () => {
      render(<Input disabled />);

      const container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass(
        "cursor-not-allowed",
        "border-ct/input-general/main/stroke/disabled",
      );
    });

    it("does not focus when wrapper is clicked while disabled", async () => {
      render(<Input disabled />);

      const wrapper = screen.getByRole("textbox").parentElement?.parentElement;
      const input = screen.getByRole("textbox");

      fireEvent.click(wrapper!);
      expect(input).not.toHaveFocus();
    });
  });

  describe("ReadOnly State", () => {
    it("renders readonly input", () => {
      render(<Input readOnly />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("readonly");
      expect(input).toHaveClass("cursor-not-allowed");
    });

    it("applies readonly container styles", () => {
      render(<Input readOnly />);

      const container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass(
        "cursor-not-allowed",
        "border-ct/input-general/main/stroke/auto-filled",
      );
    });
  });

  describe("Focus and Interaction", () => {
    it("focuses input when wrapper is clicked", async () => {
      render(<Input />);

      const wrapper = screen.getByRole("textbox").parentElement?.parentElement;
      const input = screen.getByRole("textbox");

      fireEvent.click(wrapper!);
      expect(input).toHaveFocus();
    });

    it("does not refocus when input itself is clicked", async () => {
      render(<Input />);

      const input = screen.getByRole("textbox");
      input.focus();

      const focusSpy = vi.spyOn(input, "focus");
      fireEvent.click(input);

      expect(focusSpy).not.toHaveBeenCalled();
    });

    it("handles user typing", async () => {
      const user = userEvent.setup();
      render(<Input />);

      const input = screen.getByRole("textbox");
      await user.type(input, "Hello World");

      expect(input).toHaveValue("Hello World");
    });
  });

  describe("Event Handlers", () => {
    it("calls onChange handler", async () => {
      const handleChange = vi.fn();
      const user = userEvent.setup();
      render(<Input onChange={handleChange} />);

      const input = screen.getByRole("textbox");
      await user.type(input, "test");

      expect(handleChange).toHaveBeenCalled();
    });

    it("calls onFocus handler", async () => {
      const handleFocus = vi.fn();
      render(<Input onFocus={handleFocus} />);

      const input = screen.getByRole("textbox");
      fireEvent.focus(input);

      expect(handleFocus).toHaveBeenCalled();
    });

    it("calls onBlur handler", async () => {
      const handleBlur = vi.fn();
      render(<Input onBlur={handleBlur} />);

      const input = screen.getByRole("textbox");
      fireEvent.focus(input);
      fireEvent.blur(input);

      expect(handleBlur).toHaveBeenCalled();
    });
  });

  describe("Ref Handling", () => {
    it("handles function ref", () => {
      const refFn = vi.fn();
      render(<Input ref={refFn} />);

      expect(refFn).toHaveBeenCalledWith(expect.any(HTMLInputElement));
    });

    it("handles object ref", () => {
      const ref = { current: null };
      render(<Input ref={ref} />);

      expect(ref.current).toBeInstanceOf(HTMLInputElement);
    });
  });
});

describe("InputTextArea Component", () => {
  describe("Basic Rendering", () => {
    it("renders textarea with default props", () => {
      render(<InputTextArea />);

      const textarea = screen.getByRole("textbox");
      expect(textarea).toBeInTheDocument();
      expect(textarea.tagName).toBe("TEXTAREA");
      expect(textarea).not.toBeDisabled();
      expect(textarea).not.toHaveAttribute("readonly");
    });

    it("renders with custom placeholder", () => {
      render(<InputTextArea placeholder="Enter your message" />);

      const textarea = screen.getByRole("textbox");
      expect(textarea).toHaveAttribute("placeholder", "Enter your message");
    });

    it("renders with data-slot attribute", () => {
      render(<InputTextArea />);

      const textarea = screen.getByRole("textbox");
      expect(textarea).toHaveAttribute("data-slot", "textarea");
    });
  });

  describe("States", () => {
    it("renders disabled textarea", () => {
      render(<InputTextArea disabled />);

      const textarea = screen.getByRole("textbox");
      expect(textarea).toBeDisabled();
      expect(textarea).toHaveClass("cursor-not-allowed");
    });

    it("renders readonly textarea", () => {
      render(<InputTextArea readOnly />);

      const textarea = screen.getByRole("textbox");
      expect(textarea).toHaveAttribute("readonly");
      expect(textarea).toHaveClass("cursor-not-allowed");
    });
  });

  describe("Status Styling", () => {
    it("applies correct status classes", () => {
      const { rerender } = render(<InputTextArea status="default" />);
      let container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("border-ct/input-general/main/stroke/default");

      rerender(<InputTextArea status="error" />);
      container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("border-ct/input-general/main/stroke/error");

      rerender(<InputTextArea status="success" />);
      container = screen.getByRole("textbox").parentElement;
      expect(container).toHaveClass("border-ct/input-general/main/stroke/success");
    });
  });

  describe("User Interaction", () => {
    it("handles user typing", async () => {
      const user = userEvent.setup();
      render(<InputTextArea />);

      const textarea = screen.getByRole("textbox");
      await user.type(textarea, "Hello\nWorld");

      expect(textarea).toHaveValue("Hello\nWorld");
    });

    it("calls onChange handler", async () => {
      const handleChange = vi.fn();
      const user = userEvent.setup();
      render(<InputTextArea onChange={handleChange} />);

      const textarea = screen.getByRole("textbox");
      await user.type(textarea, "test");

      expect(handleChange).toHaveBeenCalled();
    });
  });
});

describe("InputFieldWrapper Component", () => {
  it("renders wrapper with children", () => {
    render(
      <InputFieldWrapper>
        <div data-testid="child">Child content</div>
      </InputFieldWrapper>,
    );

    expect(screen.getByTestId("child")).toBeInTheDocument();
  });

  it("applies default classes", () => {
    render(
      <InputFieldWrapper>
        <div data-testid="child">Child content</div>
      </InputFieldWrapper>,
    );

    const wrapper = screen.getByTestId("child").parentElement;
    expect(wrapper).toHaveClass("relative", "flex", "flex-col", "gap-2");
  });

  it("applies custom className", () => {
    render(
      <InputFieldWrapper className="custom-wrapper">
        <div data-testid="child">Child content</div>
      </InputFieldWrapper>,
    );

    const wrapper = screen.getByTestId("child").parentElement;
    expect(wrapper).toHaveClass("custom-wrapper");
  });
});

describe("InputFieldTitle Component", () => {
  it("renders title text", () => {
    render(<InputFieldTitle>Field Label</InputFieldTitle>);

    expect(screen.getByText("Field Label")).toBeInTheDocument();
  });

  it("shows required asterisk when isRequired is true", () => {
    render(<InputFieldTitle isRequired>Required Field</InputFieldTitle>);

    expect(screen.getByText("*")).toBeInTheDocument();
    expect(screen.getByText("*")).toHaveClass("text-ct/text/danger/general");
  });

  it("does not show asterisk when isRequired is false", () => {
    render(<InputFieldTitle isRequired={false}>Optional Field</InputFieldTitle>);

    expect(screen.queryByText("*")).not.toBeInTheDocument();
  });

  it("shows info icon when info prop is provided", () => {
    render(<InputFieldTitle info="Additional information">Field with Info</InputFieldTitle>);

    expect(screen.getByTestId("info-icon")).toBeInTheDocument();
  });

  it("applies custom className", () => {
    render(<InputFieldTitle className="custom-title">Field Title</InputFieldTitle>);

    const title = screen.getByText("Field Title").parentElement;
    expect(title).toHaveClass("custom-title");
  });
});

describe("InputStatusMessage Component", () => {
  it("renders message content", () => {
    render(<InputStatusMessage status="default">Status message</InputStatusMessage>);

    expect(screen.getByText("Status message")).toBeInTheDocument();
  });

  it("applies correct status classes", () => {
    const { rerender } = render(<InputStatusMessage status="default">Message</InputStatusMessage>);
    let message = screen.getByText("Message");
    expect(message).toHaveClass("text-ct/text/main/subtlest");

    rerender(<InputStatusMessage status="error">Error message</InputStatusMessage>);
    message = screen.getByText("Error message");
    expect(message).toHaveClass("text-ct/text/danger/general");

    rerender(<InputStatusMessage status="success">Success message</InputStatusMessage>);
    message = screen.getByText("Success message");
    expect(message).toHaveClass("text-ct/text/success/general");
  });

  it("applies custom className", () => {
    render(
      <InputStatusMessage status="default" className="custom-message">
        Message
      </InputStatusMessage>,
    );

    const message = screen.getByText("Message");
    expect(message).toHaveClass("custom-message");
  });
});

describe("InputTextAreaStatusMessage Component", () => {
  it("renders message content", () => {
    render(
      <InputTextAreaStatusMessage status="default">Textarea message</InputTextAreaStatusMessage>,
    );

    expect(screen.getByText("Textarea message")).toBeInTheDocument();
  });

  it("applies correct status classes", () => {
    const { rerender } = render(
      <InputTextAreaStatusMessage status="default">Message</InputTextAreaStatusMessage>,
    );
    let message = screen.getByText("Message");
    expect(message).toHaveClass("text-ct/text/main/subtlest");

    rerender(<InputTextAreaStatusMessage status="error">Error message</InputTextAreaStatusMessage>);
    message = screen.getByText("Error message");
    expect(message).toHaveClass("text-ct/text/danger/general");

    rerender(
      <InputTextAreaStatusMessage status="success">Success message</InputTextAreaStatusMessage>,
    );
    message = screen.getByText("Success message");
    expect(message).toHaveClass("text-ct/text/success/general");
  });
});

describe("WordCounts Component", () => {
  const mockWatch = vi.fn();

  beforeEach(() => {
    mockWatch.mockClear();
  });

  it("displays current character count", () => {
    mockWatch.mockReturnValue("Hello");
    render(<WordCounts name="testField" watch={mockWatch} />);

    expect(screen.getByText("5")).toBeInTheDocument();
    expect(mockWatch).toHaveBeenCalledWith("testField");
  });

  it("displays count with maximum when provided", () => {
    mockWatch.mockReturnValue("Hello");
    render(<WordCounts name="testField" watch={mockWatch} maximum={100} />);

    expect(screen.getByText("5/100")).toBeInTheDocument();
  });

  it("handles empty string", () => {
    mockWatch.mockReturnValue("");
    render(<WordCounts name="testField" watch={mockWatch} />);

    expect(screen.getByText("0")).toBeInTheDocument();
  });

  it("handles undefined value", () => {
    mockWatch.mockReturnValue(undefined);
    render(<WordCounts name="testField" watch={mockWatch} />);

    expect(screen.getByText("0")).toBeInTheDocument();
  });
});

describe("Utility Functions", () => {
  describe("getIsValidateFailed", () => {
    it("returns undefined when form is not dirty", () => {
      expect(getIsValidateFailed(false, "error message")).toBeUndefined();
      expect(getIsValidateFailed(false, null)).toBeUndefined();
    });

    it("returns boolean value when form is dirty", () => {
      expect(getIsValidateFailed(true, "error message")).toBe(true);
      expect(getIsValidateFailed(true, null)).toBe(false);
      expect(getIsValidateFailed(true, undefined)).toBe(false);
      expect(getIsValidateFailed(true, "")).toBe(false);
    });
  });

  describe("getValidateStatus", () => {
    it("returns default when shouldValidate is false", () => {
      expect(getValidateStatus(false, true)).toBe("default");
      expect(getValidateStatus(false, false)).toBe("default");
      expect(getValidateStatus(false, undefined)).toBe("default");
    });

    it("returns default when shouldValidate is true but validateFailed is undefined", () => {
      expect(getValidateStatus(true, undefined)).toBe("default");
    });

    it("returns success when shouldValidate is true and validateFailed is false", () => {
      expect(getValidateStatus(true, false)).toBe("success");
    });

    it("returns error when shouldValidate is true and validateFailed is true", () => {
      expect(getValidateStatus(true, true)).toBe("error");
    });
  });
});

describe("Integration Tests", () => {
  it("renders complete form field with all components", () => {
    render(
      <InputFieldWrapper>
        <InputFieldTitle isRequired info="Help text">
          Username
        </InputFieldTitle>
        <Input placeholder="Enter username" status="error" />
        <InputStatusMessage status="error">Username is required</InputStatusMessage>
      </InputFieldWrapper>,
    );

    expect(screen.getByText("Username")).toBeInTheDocument();
    expect(screen.getByText("*")).toBeInTheDocument();
    expect(screen.getByTestId("info-icon")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Enter username")).toBeInTheDocument();
    expect(screen.getByText("Username is required")).toBeInTheDocument();
  });

  it("handles form validation states properly", () => {
    const { rerender } = render(
      <InputFieldWrapper>
        <Input status="default" />
        <InputStatusMessage status="default">Enter a value</InputStatusMessage>
      </InputFieldWrapper>,
    );

    expect(screen.getByText("Enter a value")).toHaveClass("text-ct/text/main/subtlest");

    rerender(
      <InputFieldWrapper>
        <Input status="error" />
        <InputStatusMessage status="error">Field is required</InputStatusMessage>
      </InputFieldWrapper>,
    );

    expect(screen.getByText("Field is required")).toHaveClass("text-ct/text/danger/general");

    rerender(
      <InputFieldWrapper>
        <Input status="success" />
        <InputStatusMessage status="success">Field is valid</InputStatusMessage>
      </InputFieldWrapper>,
    );

    expect(screen.getByText("Field is valid")).toHaveClass("text-ct/text/success/general");
  });
});
