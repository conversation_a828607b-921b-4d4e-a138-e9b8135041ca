import { render, screen, fireEvent } from "@testing-library/react";
import Button from "./index";
import React from "react";

describe("Button", () => {
  describe("Basic Rendering", () => {
    it("renders button with children", () => {
      render(<Button>Test Button</Button>);
      expect(screen.getByRole("button", { name: "Test Button" })).toBeInTheDocument();
    });

    it("renders button without children", () => {
      render(<Button />);
      expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("applies custom className", () => {
      render(<Button className="custom-class">Test</Button>);
      expect(screen.getByRole("button")).toHaveClass("custom-class");
    });

    it("applies custom width and minWidth styles", () => {
      render(
        <Button width="200px" minWidth="100px">
          Test
        </Button>,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveStyle("width: 200px");
      expect(button).toHaveStyle("min-width: 100px");
    });
  });

  describe("Button Types", () => {
    it("renders without explicit type by default", () => {
      render(<Button>Test</Button>);
      expect(screen.getByRole("button")).not.toHaveAttribute("type");
    });

    it("renders submit type when specified", () => {
      render(<Button type="submit">Test</Button>);
      expect(screen.getByRole("button")).toHaveAttribute("type", "submit");
    });

    it("renders reset type when specified", () => {
      render(<Button type="reset">Test</Button>);
      expect(screen.getByRole("button")).toHaveAttribute("type", "reset");
    });
  });

  describe("Variants", () => {
    it("renders button-filled variant by default", () => {
      render(<Button>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-at/gradient/primary/default");
    });

    it("renders button-stroke variant", () => {
      render(<Button variant="button-stroke">Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass(
        "bg-transparent",
        "border",
        "border-ct/button-stroke/main/stroke/default",
      );
    });
  });

  describe("Colors", () => {
    it("renders main color by default", () => {
      render(<Button>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-at/gradient/primary/default");
    });

    it("renders moderate color", () => {
      render(<Button color="moderate">Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-ct/button-filled/moderate/default");
    });

    it("renders inverse color", () => {
      render(<Button color="inverse">Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-ct/button-filled/inverse/default");
    });

    it("renders moderate button-stroke variant", () => {
      render(
        <Button color="moderate" variant="button-stroke">
          Test
        </Button>,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass("border-ct/button-stroke/moderate/stroke/default");
    });

    it("renders inverse button-stroke variant", () => {
      render(
        <Button color="inverse" variant="button-stroke">
          Test
        </Button>,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass("border-ct/button-stroke/inverse/stroke/default");
    });
  });

  describe("Sizes", () => {
    it("renders xl size by default", () => {
      render(<Button>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("px-3", "min-h-12", "text-button.xxlg");
    });

    it("renders small size", () => {
      render(<Button size="s">Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("px-1.5", "min-h-7", "text-button.md");
    });

    it("renders medium size", () => {
      render(<Button size="m">Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("px-2", "min-h-9", "text-button.lg");
    });

    it("renders large size", () => {
      render(<Button size="l">Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("px-2.5", "min-h-10", "text-button.lg");
    });
  });

  describe("Disabled State", () => {
    it("renders disabled button", () => {
      render(<Button disabled>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toBeDisabled();
      expect(button).toHaveClass("disabled:cursor-not-allowed");
    });

    it("applies disabled color styles for main filled variant", () => {
      render(<Button disabled>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("disabled:bg-ct/button-filled/main/disabled");
    });

    it("applies disabled color styles for stroke variant", () => {
      render(
        <Button disabled variant="button-stroke">
          Test
        </Button>,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass("disabled:bg-ct/button-stroke/main/stroke/disabled");
    });
  });

  describe("Active State", () => {
    it("applies active styles when isActive is true", () => {
      render(<Button isActive>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-ct/button-filled/main/active");
    });

    it("applies active styles for moderate color", () => {
      render(
        <Button isActive color="moderate">
          Test
        </Button>,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-ct/button-filled/moderate/active");
    });

    it("applies active styles for stroke variant", () => {
      render(
        <Button isActive variant="button-stroke">
          Test
        </Button>,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-ct/button-stroke/main/container/active");
    });

    it("does not apply active styles when isActive is false", () => {
      render(<Button isActive={false}>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).not.toHaveClass("bg-ct/button-filled/main/active");
    });
  });

  describe("Prefix and Postfix", () => {
    it("renders with prefix", () => {
      const prefix = <span data-testid="prefix">→</span>;
      render(<Button prefix={prefix}>Test</Button>);
      expect(screen.getByTestId("prefix")).toBeInTheDocument();
      expect(screen.getByText("Test")).toBeInTheDocument();
    });

    it("renders with postfix", () => {
      const postfix = <span data-testid="postfix">←</span>;
      render(<Button postfix={postfix}>Test</Button>);
      expect(screen.getByTestId("postfix")).toBeInTheDocument();
      expect(screen.getByText("Test")).toBeInTheDocument();
    });

    it("renders with both prefix and postfix", () => {
      const prefix = <span data-testid="prefix">→</span>;
      const postfix = <span data-testid="postfix">←</span>;
      render(
        <Button prefix={prefix} postfix={postfix}>
          Test
        </Button>,
      );
      expect(screen.getByTestId("prefix")).toBeInTheDocument();
      expect(screen.getByTestId("postfix")).toBeInTheDocument();
      expect(screen.getByText("Test")).toBeInTheDocument();
    });

    it("applies justify-center when no prefix", () => {
      render(<Button>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("justify-center");
    });

    it("does not apply justify-center when prefix exists", () => {
      const prefix = <span>→</span>;
      render(<Button prefix={prefix}>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).not.toHaveClass("justify-center");
    });
  });

  describe("Event Handling", () => {
    it("calls onClick when clicked", () => {
      const handleClick = vi.fn();
      render(<Button onClick={handleClick}>Test</Button>);
      fireEvent.click(screen.getByRole("button"));
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it("calls onClick with event parameter", () => {
      const handleClick = vi.fn();
      render(<Button onClick={handleClick}>Test</Button>);
      const button = screen.getByRole("button");
      fireEvent.click(button);
      expect(handleClick).toHaveBeenCalledWith(expect.any(Object));
    });

    it("does not call onClick when disabled", () => {
      const handleClick = vi.fn();
      render(
        <Button onClick={handleClick} disabled>
          Test
        </Button>,
      );
      fireEvent.click(screen.getByRole("button"));
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe("Accessibility", () => {
    it("has proper role", () => {
      render(<Button>Test</Button>);
      expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("is focusable when not disabled", () => {
      render(<Button>Test</Button>);
      const button = screen.getByRole("button");
      button.focus();
      expect(button).toHaveFocus();
    });

    it("is not focusable when disabled", () => {
      render(<Button disabled>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toBeDisabled();
    });
  });

  describe("Component Structure", () => {
    it("has correct base classes", () => {
      render(<Button>Test</Button>);
      const button = screen.getByRole("button");
      expect(button).toHaveClass("flex", "items-center", "disabled:cursor-not-allowed");
    });

    it("renders children in a span with correct padding", () => {
      render(<Button size="s">Test Content</Button>);
      const childSpan = screen.getByText("Test Content");
      expect(childSpan.tagName).toBe("SPAN");
      expect(childSpan).toHaveClass("px-1.5");
    });

    it("applies correct children padding for different sizes", () => {
      const { rerender } = render(<Button size="m">Test</Button>);
      expect(screen.getByText("Test")).toHaveClass("px-2");

      rerender(<Button size="l">Test</Button>);
      expect(screen.getByText("Test")).toHaveClass("px-2.5");

      rerender(<Button size="xl">Test</Button>);
      expect(screen.getByText("Test")).toHaveClass("px-3");
    });
  });

  describe("Memoization", () => {
    it("has correct display name", () => {
      expect(Button.displayName).toBe("Button");
    });
  });

  describe("Edge Cases", () => {
    it("handles empty children gracefully", () => {
      render(<Button>{""}</Button>);
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("handles null children", () => {
      render(<Button>{null}</Button>);
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("handles undefined prefix and postfix", () => {
      render(
        <Button prefix={undefined} postfix={undefined}>
          Test
        </Button>,
      );
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
      expect(screen.getByText("Test")).toBeInTheDocument();
    });
  });
});
