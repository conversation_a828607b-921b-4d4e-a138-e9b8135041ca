import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useState } from "react";
import { Collapse, useCollapse } from "./index";
import EasyToUseCollapse from "./index";

// Mock external dependencies
vi.mock("@pcsc/icons/directional", () => ({
  Down: ({ className, ...props }: any) => (
    <div data-testid="down-icon" className={className} {...props}>
      DownIcon
    </div>
  ),
}));

// Mock ResizeObserver
const mockResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn(),
}));

vi.stubGlobal("ResizeObserver", mockResizeObserver);

// Helper component to test useCollapse hook
const TestHookComponent = () => {
  const context = useCollapse();
  return (
    <div data-testid="hook-test">
      <span data-testid="is-collapsed">{context.isCollapsed.toString()}</span>
      <span data-testid="max-height">{context.maxHeight}</span>
      <span data-testid="should-show-trigger">{context.shouldShowTrigger.toString()}</span>
      <span data-testid="content-exceeds">{context.contentExceedsMaxHeight.toString()}</span>
      <button data-testid="toggle-button" onClick={context.toggleCollapse}>
        Toggle
      </button>
    </div>
  );
};

describe("Collapse.Root Component", () => {
  describe("Basic Rendering", () => {
    it("renders children within context provider", () => {
      render(
        <Collapse.Root>
          <div data-testid="child-content">Test content</div>
        </Collapse.Root>,
      );

      expect(screen.getByTestId("child-content")).toBeInTheDocument();
    });

    it("provides default context values", () => {
      render(
        <Collapse.Root>
          <TestHookComponent />
        </Collapse.Root>,
      );

      expect(screen.getByTestId("is-collapsed")).toHaveTextContent("true");
      expect(screen.getByTestId("max-height")).toHaveTextContent("220");
      expect(screen.getByTestId("should-show-trigger")).toHaveTextContent("true");
      expect(screen.getByTestId("content-exceeds")).toHaveTextContent("false");
    });

    it("accepts custom maxHeight prop", () => {
      render(
        <Collapse.Root maxHeight={300}>
          <TestHookComponent />
        </Collapse.Root>,
      );

      expect(screen.getByTestId("max-height")).toHaveTextContent("300");
    });

    it("accepts shouldShowTrigger prop", () => {
      render(
        <Collapse.Root shouldShowTrigger={false}>
          <TestHookComponent />
        </Collapse.Root>,
      );

      expect(screen.getByTestId("should-show-trigger")).toHaveTextContent("false");
    });
  });

  describe("State Management", () => {
    it("toggles collapsed state", async () => {
      render(
        <Collapse.Root>
          <TestHookComponent />
        </Collapse.Root>,
      );

      const toggleButton = screen.getByTestId("toggle-button");
      const collapseState = screen.getByTestId("is-collapsed");

      expect(collapseState).toHaveTextContent("true");

      fireEvent.click(toggleButton);
      expect(collapseState).toHaveTextContent("false");

      fireEvent.click(toggleButton);
      expect(collapseState).toHaveTextContent("true");
    });

    it("calls triggerCloseCallback when collapsing", async () => {
      const mockCallback = vi.fn();
      render(
        <Collapse.Root triggerCloseCallback={mockCallback}>
          <TestHookComponent />
        </Collapse.Root>,
      );

      const toggleButton = screen.getByTestId("toggle-button");

      // First click to expand
      fireEvent.click(toggleButton);
      expect(mockCallback).not.toHaveBeenCalled();

      // Second click to collapse should trigger callback
      fireEvent.click(toggleButton);
      expect(mockCallback).toHaveBeenCalledTimes(1);
    });
  });
});

describe("Collapse.Content Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Basic Rendering", () => {
    it("renders content with children", () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div data-testid="content-child">Content text</div>
          </Collapse.Content>
        </Collapse.Root>,
      );

      expect(screen.getByTestId("content-child")).toBeInTheDocument();
    });

    it("applies default CSS classes", () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div data-testid="content">Content</div>
          </Collapse.Content>
        </Collapse.Root>,
      );

      const contentContainer = screen.getByTestId("content").closest('[class*="overflow-hidden"]');
      expect(contentContainer).toHaveClass(
        "relative",
        "overflow-hidden",
        "transition-all",
        "duration-300",
        "ease-in-out",
      );
    });

    it("applies custom className", () => {
      render(
        <Collapse.Root>
          <Collapse.Content className="custom-content-class">
            <div data-testid="content">Content</div>
          </Collapse.Content>
        </Collapse.Root>,
      );

      const contentContainer = screen
        .getByTestId("content")
        .closest('[class*="custom-content-class"]');
      expect(contentContainer).toHaveClass("custom-content-class");
    });
  });

  describe("Height Management", () => {
    it("sets correct max-height style when collapsed", () => {
      render(
        <Collapse.Root maxHeight={150}>
          <Collapse.Content>
            <div data-testid="content">Content</div>
          </Collapse.Content>
        </Collapse.Root>,
      );

      const contentContainer = screen.getByTestId("content").parentElement?.parentElement;
      expect(contentContainer).toHaveStyle("max-height: 150px");
    });

    it("sets up ResizeObserver on mount", () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div data-testid="content">Content</div>
          </Collapse.Content>
        </Collapse.Root>,
      );

      expect(mockResizeObserver).toHaveBeenCalled();
    });

    it("renders gradient mask when collapsed and content exceeds height", () => {
      // Mock scrollHeight to simulate content exceeding maxHeight
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 300, // Greater than default maxHeight of 220
      });

      render(
        <Collapse.Root>
          <Collapse.Content>
            <div data-testid="content">Long content that exceeds height</div>
          </Collapse.Content>
        </Collapse.Root>,
      );

      // The gradient mask should be present when collapsed and content exceeds height
      const gradientMask = screen
        .getByTestId("content")
        .parentElement?.parentElement?.querySelector('[class*="bg-gradient-to-t"]');
      expect(gradientMask).toBeInTheDocument();
      expect(gradientMask).toHaveAttribute("aria-hidden", "true");
    });
  });

  describe("Error Handling", () => {
    it("throws error when used outside Collapse.Root", () => {
      // Suppress console.error for this test
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      expect(() => {
        render(
          <Collapse.Content>
            <div>Content</div>
          </Collapse.Content>,
        );
      }).toThrow("useCollapse must be used within an Collapse.Root");

      consoleSpy.mockRestore();
    });
  });
});

describe("Collapse.Trigger Component", () => {
  describe("Basic Rendering", () => {
    it("renders default trigger button when no children provided", () => {
      // Mock content exceeding height to show trigger
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 300,
      });

      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger />
        </Collapse.Root>,
      );

      expect(screen.getByRole("button")).toBeInTheDocument();
      expect(screen.getByText("查看更多")).toBeInTheDocument();
      expect(screen.getByTestId("down-icon")).toBeInTheDocument();
    });

    it("renders custom trigger content when children provided", () => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 300,
      });

      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger>
            <button data-testid="custom-trigger">Custom Trigger</button>
          </Collapse.Trigger>
        </Collapse.Root>,
      );

      expect(screen.getByTestId("custom-trigger")).toBeInTheDocument();
      expect(screen.getByText("Custom Trigger")).toBeInTheDocument();
    });

    it("does not render when content does not exceed maxHeight", () => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 100, // Less than default maxHeight of 220
      });

      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Short content</div>
          </Collapse.Content>
          <Collapse.Trigger />
        </Collapse.Root>,
      );

      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("does not render when shouldShowTrigger is false", () => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 300,
      });

      render(
        <Collapse.Root shouldShowTrigger={false}>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger />
        </Collapse.Root>,
      );

      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("applies custom className to custom trigger", () => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 300,
      });

      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger className="custom-trigger-class">
            <button>Custom</button>
          </Collapse.Trigger>
        </Collapse.Root>,
      );

      const customTrigger = screen.getByText("Custom").closest('[class*="custom-trigger-class"]');
      expect(customTrigger).toHaveClass("custom-trigger-class");
    });
  });

  describe("Default Trigger Button", () => {
    beforeEach(() => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 300,
      });
    });

    it("applies correct styling classes", () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger />
        </Collapse.Root>,
      );

      const button = screen.getByRole("button");
      expect(button).toHaveClass(
        "group",
        "absolute",
        "left-[50%]",
        "translate-x-[-50%]",
        "translate-y-[-50%]",
        "rounded-[400px]",
        "bg-ct/button-rounded/moderate/default",
        "py-2",
        "pl-4",
        "pr-2",
      );
    });

    it("updates button text and icon rotation based on collapsed state", async () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger />
        </Collapse.Root>,
      );

      const button = screen.getByRole("button");
      const icon = screen.getByTestId("down-icon");

      // Initially collapsed
      expect(screen.getByText("查看更多")).toBeInTheDocument();
      expect(icon).not.toHaveClass("rotate-180");

      // Click to expand
      fireEvent.click(button);
      await waitFor(() => {
        expect(screen.getByText("顯示較少")).toBeInTheDocument();
        expect(icon).toHaveClass("rotate-180");
      });

      // Click to collapse
      fireEvent.click(button);
      await waitFor(() => {
        expect(screen.getByText("查看更多")).toBeInTheDocument();
        expect(icon).not.toHaveClass("rotate-180");
      });
    });

    it("sets correct aria-expanded attribute", async () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger />
        </Collapse.Root>,
      );

      const button = screen.getByRole("button");

      // Initially collapsed (aria-expanded should be false)
      expect(button).toHaveAttribute("aria-expanded", "false");

      // Click to expand
      fireEvent.click(button);
      expect(button).toHaveAttribute("aria-expanded", "true");
    });

    it("sets aria-hidden on icon", () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger />
        </Collapse.Root>,
      );

      expect(screen.getByTestId("down-icon")).toHaveAttribute("aria-hidden", "true");
    });
  });

  describe("Custom Trigger Interaction", () => {
    beforeEach(() => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 300,
      });
    });

    it("handles click events on custom trigger", async () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div data-testid="content">Long content</div>
          </Collapse.Content>
          <Collapse.Trigger>
            <button data-testid="custom-trigger">Toggle</button>
          </Collapse.Trigger>
        </Collapse.Root>,
      );

      const customTrigger = screen.getByTestId("custom-trigger").parentElement;
      const content = screen.getByTestId("content").parentElement?.parentElement;

      // Initially collapsed
      expect(content).toHaveStyle("max-height: 220px");

      // Click custom trigger
      fireEvent.click(customTrigger!);

      await waitFor(() => {
        expect(content).toHaveStyle("max-height: 300px");
      });
    });

    it("handles keyboard events (Enter and Space)", async () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div data-testid="content">Long content</div>
          </Collapse.Content>
          <Collapse.Trigger>
            <span data-testid="custom-trigger">Toggle</span>
          </Collapse.Trigger>
        </Collapse.Root>,
      );

      const customTrigger = screen.getByTestId("custom-trigger").parentElement;

      // Test Enter key
      fireEvent.keyDown(customTrigger!, { key: "Enter" });
      await waitFor(() => {
        expect(customTrigger).toHaveAttribute("aria-expanded", "true");
      });

      // Test Space key
      fireEvent.keyDown(customTrigger!, { key: " " });
      await waitFor(() => {
        expect(customTrigger).toHaveAttribute("aria-expanded", "false");
      });
    });

    it("prevents default behavior on Enter and Space keys", () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger>
            <span>Toggle</span>
          </Collapse.Trigger>
        </Collapse.Root>,
      );

      const customTrigger = screen.getByText("Toggle").parentElement;

      // Use fireEvent.keyDown which properly handles preventDefault
      fireEvent.keyDown(customTrigger!, {
        key: "Enter",
        preventDefault: vi.fn(),
      });

      fireEvent.keyDown(customTrigger!, {
        key: " ",
        preventDefault: vi.fn(),
      });

      // Test that the events were handled (component should toggle state)
      expect(customTrigger).toHaveAttribute("aria-expanded", "false");
    });

    it("ignores other keyboard events", () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger>
            <span>Toggle</span>
          </Collapse.Trigger>
        </Collapse.Root>,
      );

      const customTrigger = screen.getByText("Toggle").parentElement;

      fireEvent.keyDown(customTrigger!, { key: "Tab" });
      fireEvent.keyDown(customTrigger!, { key: "Escape" });

      // Should not change state
      expect(customTrigger).toHaveAttribute("aria-expanded", "false");
    });

    it("sets correct accessibility attributes on custom trigger", () => {
      render(
        <Collapse.Root>
          <Collapse.Content>
            <div>Long content</div>
          </Collapse.Content>
          <Collapse.Trigger>
            <span>Toggle</span>
          </Collapse.Trigger>
        </Collapse.Root>,
      );

      const customTrigger = screen.getByText("Toggle").parentElement;
      expect(customTrigger).toHaveAttribute("role", "button");
      expect(customTrigger).toHaveAttribute("tabIndex", "0");
      expect(customTrigger).toHaveAttribute("aria-expanded", "false");
    });
  });

  describe("Error Handling", () => {
    it("throws error when used outside Collapse.Root", () => {
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      expect(() => {
        render(<Collapse.Trigger />);
      }).toThrow("useCollapse must be used within an Collapse.Root");

      consoleSpy.mockRestore();
    });
  });
});

describe("EasyToUseCollapse Component", () => {
  describe("Basic Rendering", () => {
    it("renders with default props", () => {
      render(
        <EasyToUseCollapse>
          <div data-testid="easy-content">Easy content</div>
        </EasyToUseCollapse>,
      );

      expect(screen.getByTestId("easy-content")).toBeInTheDocument();
    });

    it("accepts maxHeight prop", () => {
      render(
        <EasyToUseCollapse maxHeight={300}>
          <div data-testid="easy-content">Easy content</div>
        </EasyToUseCollapse>,
      );

      const content = screen.getByTestId("easy-content").parentElement?.parentElement;
      expect(content).toHaveStyle("max-height: 300px");
    });

    it("applies custom className to content", () => {
      render(
        <EasyToUseCollapse className="easy-custom-class">
          <div data-testid="easy-content">Easy content</div>
        </EasyToUseCollapse>,
      );

      const contentContainer = screen
        .getByTestId("easy-content")
        .closest('[class*="easy-custom-class"]');
      expect(contentContainer).toHaveClass("easy-custom-class");
    });

    it("accepts shouldShowTrigger prop", () => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 300,
      });

      render(
        <EasyToUseCollapse shouldShowTrigger={false}>
          <div>Long content that exceeds height</div>
        </EasyToUseCollapse>,
      );

      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("accepts and calls triggerCloseCallback", async () => {
      const mockCallback = vi.fn();
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 300,
      });

      render(
        <EasyToUseCollapse triggerCloseCallback={mockCallback}>
          <div>Long content</div>
        </EasyToUseCollapse>,
      );

      const button = screen.getByRole("button");

      // Expand first
      fireEvent.click(button);
      expect(mockCallback).not.toHaveBeenCalled();

      // Then collapse
      fireEvent.click(button);
      expect(mockCallback).toHaveBeenCalledTimes(1);
    });
  });

  describe("Integration Behavior", () => {
    it("shows trigger button when content exceeds maxHeight", () => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 300,
      });

      render(
        <EasyToUseCollapse maxHeight={200}>
          <div>Long content that definitely exceeds 200px height</div>
        </EasyToUseCollapse>,
      );

      expect(screen.getByRole("button")).toBeInTheDocument();
      expect(screen.getByText("查看更多")).toBeInTheDocument();
    });

    it("hides trigger button when content is short", () => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 100,
      });

      render(
        <EasyToUseCollapse maxHeight={200}>
          <div>Short content</div>
        </EasyToUseCollapse>,
      );

      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("toggles content height when trigger is clicked", async () => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 400,
      });

      render(
        <EasyToUseCollapse maxHeight={200}>
          <div data-testid="toggle-content">Very long content</div>
        </EasyToUseCollapse>,
      );

      const content = screen.getByTestId("toggle-content").parentElement?.parentElement;
      const button = screen.getByRole("button");

      // Initially collapsed
      expect(content).toHaveStyle("max-height: 200px");
      expect(screen.getByText("查看更多")).toBeInTheDocument();

      // Click to expand
      fireEvent.click(button);
      await waitFor(() => {
        expect(content).toHaveStyle("max-height: 400px");
        expect(screen.getByText("顯示較少")).toBeInTheDocument();
      });

      // Click to collapse
      fireEvent.click(button);
      await waitFor(() => {
        expect(content).toHaveStyle("max-height: 200px");
        expect(screen.getByText("查看更多")).toBeInTheDocument();
      });
    });
  });

  describe("Edge Cases", () => {
    it("handles zero maxHeight", () => {
      render(
        <EasyToUseCollapse maxHeight={0}>
          <div data-testid="zero-height-content">Content</div>
        </EasyToUseCollapse>,
      );

      const content = screen.getByTestId("zero-height-content").parentElement?.parentElement;
      expect(content).toHaveStyle("max-height: 0px");
    });

    it("handles very large maxHeight", () => {
      render(
        <EasyToUseCollapse maxHeight={10000}>
          <div data-testid="large-height-content">Content</div>
        </EasyToUseCollapse>,
      );

      const content = screen.getByTestId("large-height-content").parentElement?.parentElement;
      expect(content).toHaveStyle("max-height: 10000px");
    });

    it("handles complex nested content", () => {
      Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
        configurable: true,
        value: 500,
      });

      render(
        <EasyToUseCollapse maxHeight={300}>
          <div data-testid="complex-content">
            <h1>Title</h1>
            <div>
              <p>Paragraph 1</p>
              <ul>
                <li>Item 1</li>
                <li>Item 2</li>
              </ul>
              <p>Paragraph 2</p>
            </div>
          </div>
        </EasyToUseCollapse>,
      );

      expect(screen.getByTestId("complex-content")).toBeInTheDocument();
      expect(screen.getByText("Title")).toBeInTheDocument();
      expect(screen.getByText("Item 1")).toBeInTheDocument();
      expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("handles dynamic content changes", async () => {
      const DynamicContent = () => {
        const [items, setItems] = useState(["Item 1"]);

        return (
          <EasyToUseCollapse maxHeight={100}>
            <div data-testid="dynamic-content">
              {items.map((item, index) => (
                <div key={index} style={{ height: "50px" }}>
                  {item}
                </div>
              ))}
              <button
                data-testid="add-item"
                onClick={() => setItems((prev) => [...prev, `Item ${prev.length + 1}`])}
              >
                Add Item
              </button>
            </div>
          </EasyToUseCollapse>
        );
      };

      render(<DynamicContent />);

      const addButton = screen.getByTestId("add-item");
      fireEvent.click(addButton);

      await waitFor(() => {
        expect(screen.getByText("Item 2")).toBeInTheDocument();
      });
    });
  });
});

describe("useCollapse Hook", () => {
  describe("Error Handling", () => {
    it("throws error when used outside context", () => {
      const TestComponent = () => {
        useCollapse();
        return <div>Test</div>;
      };

      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      expect(() => {
        render(<TestComponent />);
      }).toThrow("useCollapse must be used within an Collapse.Root");

      consoleSpy.mockRestore();
    });
  });

  describe("Context Values", () => {
    it("provides all required context values", () => {
      render(
        <Collapse.Root maxHeight={150} shouldShowTrigger={false}>
          <TestHookComponent />
        </Collapse.Root>,
      );

      expect(screen.getByTestId("is-collapsed")).toHaveTextContent("true");
      expect(screen.getByTestId("max-height")).toHaveTextContent("150");
      expect(screen.getByTestId("should-show-trigger")).toHaveTextContent("false");
      expect(screen.getByTestId("content-exceeds")).toHaveTextContent("false");
    });
  });
});
