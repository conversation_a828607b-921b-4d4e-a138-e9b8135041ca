import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import {
  AccordionList,
  AccordionI<PERSON>,
  AccordionTrigger,
  Accordion<PERSON>ontent,
  AccordionOrigin,
} from "./index";

// Mock external dependencies
vi.mock("@radix-ui/react-accordion", () => ({
  Root: ({ children, className, collapsible, ...props }: any) => (
    <div
      data-testid="accordion-root"
      className={className}
      {...(collapsible !== undefined ? { collapsible: collapsible ? "" : undefined } : {})}
      {...props}
    >
      {children}
    </div>
  ),
  Item: ({ children, className, value, ...props }: any) => (
    <div data-testid="accordion-item" className={className} data-value={value} {...props}>
      {children}
    </div>
  ),
  Header: ({ children, className, ...props }: any) => (
    <div data-testid="accordion-header" className={className} {...props}>
      {children}
    </div>
  ),
  Trigger: ({ children, className, ...props }: any) => (
    <button data-testid="accordion-trigger" className={className} {...props}>
      {children}
    </button>
  ),
  Content: ({ children, className, ...props }: any) => (
    <div data-testid="accordion-content" className={className} {...props}>
      {children}
    </div>
  ),
}));

vi.mock("@pcsc/icons/directional", () => ({
  Down: ({ className, ...props }: any) => (
    <div data-testid="down-icon" className={className} {...props}>
      DownIcon
    </div>
  ),
}));

describe("AccordionOrigin Component", () => {
  describe("Basic Rendering", () => {
    it("renders accordion root with children", () => {
      render(
        <AccordionOrigin>
          <div data-testid="child">Child content</div>
        </AccordionOrigin>,
      );

      expect(screen.getByTestId("accordion-root")).toBeInTheDocument();
      expect(screen.getByTestId("child")).toBeInTheDocument();
    });

    it("applies data-slot attribute", () => {
      render(
        <AccordionOrigin>
          <div>Content</div>
        </AccordionOrigin>,
      );

      expect(screen.getByTestId("accordion-root")).toHaveAttribute("data-slot", "accordion");
    });

    it("forwards props to Radix Root", () => {
      render(
        <AccordionOrigin type="single" collapsible data-custom="test">
          <div>Content</div>
        </AccordionOrigin>,
      );

      const root = screen.getByTestId("accordion-root");
      expect(root).toHaveAttribute("type", "single");
      expect(root).toHaveAttribute("collapsible", "");
      expect(root).toHaveAttribute("data-custom", "test");
    });
  });
});

describe("AccordionItem Component", () => {
  describe("Basic Rendering", () => {
    it("renders item with children", () => {
      render(
        <AccordionItem value="item-1">
          <div data-testid="item-child">Item content</div>
        </AccordionItem>,
      );

      expect(screen.getByTestId("accordion-item")).toBeInTheDocument();
      expect(screen.getByTestId("item-child")).toBeInTheDocument();
    });

    it("applies data-slot attribute", () => {
      render(
        <AccordionItem value="item-1">
          <div>Content</div>
        </AccordionItem>,
      );

      expect(screen.getByTestId("accordion-item")).toHaveAttribute("data-slot", "accordion-item");
    });

    it("applies default classes", () => {
      render(
        <AccordionItem value="item-1">
          <div>Content</div>
        </AccordionItem>,
      );

      expect(screen.getByTestId("accordion-item")).toHaveClass("border-b", "last:border-b-0");
    });

    it("applies custom className", () => {
      render(
        <AccordionItem value="item-1" className="custom-item">
          <div>Content</div>
        </AccordionItem>,
      );

      expect(screen.getByTestId("accordion-item")).toHaveClass("custom-item");
    });

    it("forwards value prop", () => {
      render(
        <AccordionItem value="custom-value">
          <div>Content</div>
        </AccordionItem>,
      );

      expect(screen.getByTestId("accordion-item")).toHaveAttribute("data-value", "custom-value");
    });
  });
});

describe("AccordionTrigger Component", () => {
  describe("Basic Rendering", () => {
    it("renders trigger button with children", () => {
      render(
        <AccordionTrigger>
          <span data-testid="trigger-content">Click me</span>
        </AccordionTrigger>,
      );

      expect(screen.getByTestId("accordion-header")).toBeInTheDocument();
      expect(screen.getByTestId("accordion-trigger")).toBeInTheDocument();
      expect(screen.getByTestId("trigger-content")).toBeInTheDocument();
    });

    it("applies data-slot attribute", () => {
      render(<AccordionTrigger>Trigger</AccordionTrigger>);

      expect(screen.getByTestId("accordion-trigger")).toHaveAttribute(
        "data-slot",
        "accordion-trigger",
      );
    });

    it("applies default classes", () => {
      render(<AccordionTrigger>Trigger</AccordionTrigger>);

      const trigger = screen.getByTestId("accordion-trigger");
      expect(trigger).toHaveClass(
        "flex",
        "flex-1",
        "items-start",
        "justify-between",
        "gap-4",
        "rounded-md",
        "py-4",
        "text-left",
        "text-sm",
        "font-medium",
      );
    });

    it("applies header wrapper classes", () => {
      render(<AccordionTrigger>Trigger</AccordionTrigger>);

      expect(screen.getByTestId("accordion-header")).toHaveClass("flex");
    });

    it("applies custom className", () => {
      render(<AccordionTrigger className="custom-trigger">Trigger</AccordionTrigger>);

      expect(screen.getByTestId("accordion-trigger")).toHaveClass("custom-trigger");
    });

    it("applies focus and interaction states classes", () => {
      render(<AccordionTrigger>Trigger</AccordionTrigger>);

      const trigger = screen.getByTestId("accordion-trigger");
      expect(trigger).toHaveClass(
        "focus-visible:border-ring",
        "focus-visible:ring-ring/50",
        "focus-visible:ring-[3px]",
        "disabled:pointer-events-none",
        "disabled:opacity-50",
        "[&[data-state=open]>svg]:rotate-180",
      );
    });
  });

  describe("Event Handling", () => {
    it("handles click events", async () => {
      const handleClick = vi.fn();
      render(<AccordionTrigger onClick={handleClick}>Trigger</AccordionTrigger>);

      const trigger = screen.getByTestId("accordion-trigger");
      fireEvent.click(trigger);

      expect(handleClick).toHaveBeenCalled();
    });

    it("forwards other props", () => {
      render(
        <AccordionTrigger disabled data-custom="test">
          Trigger
        </AccordionTrigger>,
      );

      const trigger = screen.getByTestId("accordion-trigger");
      expect(trigger).toHaveAttribute("disabled");
      expect(trigger).toHaveAttribute("data-custom", "test");
    });
  });
});

describe("AccordionContent Component", () => {
  describe("Basic Rendering", () => {
    it("renders content with children", () => {
      render(
        <AccordionContent>
          <div data-testid="content-child">Content text</div>
        </AccordionContent>,
      );

      expect(screen.getByTestId("accordion-content")).toBeInTheDocument();
      expect(screen.getByTestId("content-child")).toBeInTheDocument();
    });

    it("applies data-slot attribute", () => {
      render(<AccordionContent>Content</AccordionContent>);

      expect(screen.getByTestId("accordion-content")).toHaveAttribute(
        "data-slot",
        "accordion-content",
      );
    });

    it("applies default animation classes", () => {
      render(<AccordionContent>Content</AccordionContent>);

      expect(screen.getByTestId("accordion-content")).toHaveClass(
        "data-[state=closed]:animate-accordion-up",
        "data-[state=open]:animate-accordion-down",
        "overflow-hidden",
        "text-sm",
      );
    });

    it("applies default inner wrapper classes", () => {
      render(<AccordionContent>Content</AccordionContent>);

      const contentWrapper = screen.getByText("Content");
      expect(contentWrapper).toHaveClass("pb-4", "pt-0");
    });

    it("applies custom className to inner wrapper", () => {
      render(<AccordionContent className="custom-content">Content</AccordionContent>);

      const contentWrapper = screen.getByText("Content");
      expect(contentWrapper).toHaveClass("custom-content");
    });

    it("forwards props to Radix Content", () => {
      render(<AccordionContent data-custom="test">Content</AccordionContent>);

      expect(screen.getByTestId("accordion-content")).toHaveAttribute("data-custom", "test");
    });
  });
});

describe("AccordionList Component", () => {
  const mockData = [
    {
      title: "First Item",
      content: "First content",
    },
    {
      title: "Second Item",
      content: "Second content",
      value: "custom-value",
    },
    {
      title: <span data-testid="jsx-title">JSX Title</span>,
      content: <div data-testid="jsx-content">JSX Content</div>,
    },
  ];

  describe("Basic Rendering", () => {
    it("renders accordion list with data", () => {
      render(<AccordionList data={mockData} type="single" />);

      expect(screen.getByTestId("accordion-root")).toBeInTheDocument();
      expect(screen.getByText("First Item")).toBeInTheDocument();
      expect(screen.getByText("Second Item")).toBeInTheDocument();
      expect(screen.getByTestId("jsx-title")).toBeInTheDocument();
    });

    it("renders all accordion items", () => {
      render(<AccordionList data={mockData} type="single" />);

      const items = screen.getAllByTestId("accordion-item");
      expect(items).toHaveLength(3);
    });

    it("renders all triggers and contents", () => {
      render(<AccordionList data={mockData} type="single" />);

      const triggers = screen.getAllByTestId("accordion-trigger");
      const contents = screen.getAllByTestId("accordion-content");

      expect(triggers).toHaveLength(3);
      expect(contents).toHaveLength(3);
    });

    it("applies default className to root", () => {
      render(<AccordionList data={mockData} type="single" />);

      expect(screen.getByTestId("accordion-root")).toHaveClass("w-full");
    });

    it("applies custom className to root", () => {
      render(<AccordionList data={mockData} className="custom-accordion" type="single" />);

      expect(screen.getByTestId("accordion-root")).toHaveClass("custom-accordion");
    });
  });

  describe("Data Handling", () => {
    it("generates default values when not provided", () => {
      render(<AccordionList data={mockData} type="single" />);

      const items = screen.getAllByTestId("accordion-item");
      expect(items[0]).toHaveAttribute("data-value", "item-0");
      expect(items[1]).toHaveAttribute("data-value", "custom-value"); // Uses provided value
      expect(items[2]).toHaveAttribute("data-value", "item-2");
    });

    it("uses provided values when available", () => {
      const dataWithValues = [
        { title: "Item 1", content: "Content 1", value: "first" },
        { title: "Item 2", content: "Content 2", value: "second" },
      ];

      render(<AccordionList data={dataWithValues} type="single" />);

      const items = screen.getAllByTestId("accordion-item");
      expect(items[0]).toHaveAttribute("data-value", "first");
      expect(items[1]).toHaveAttribute("data-value", "second");
    });

    it("handles empty data array", () => {
      render(<AccordionList data={[]} type="single" />);

      expect(screen.getByTestId("accordion-root")).toBeInTheDocument();
      expect(screen.queryByTestId("accordion-item")).not.toBeInTheDocument();
    });

    it("handles JSX content in title and content", () => {
      render(<AccordionList data={mockData} type="single" />);

      expect(screen.getByTestId("jsx-title")).toBeInTheDocument();
      expect(screen.getByTestId("jsx-content")).toBeInTheDocument();
    });
  });

  describe("Styling and Classes", () => {
    it("applies correct classes to accordion items", () => {
      render(<AccordionList data={mockData} type="single" />);

      const items = screen.getAllByTestId("accordion-item");
      items.forEach((item) => {
        expect(item).toHaveClass("accordion-item");
      });
    });

    it("applies correct classes to triggers", () => {
      render(<AccordionList data={mockData} type="single" />);

      const triggers = screen.getAllByTestId("accordion-trigger");
      triggers.forEach((trigger) => {
        expect(trigger).toHaveClass(
          "accordion-trigger",
          "flex",
          "items-center",
          "p-5",
          "hover:text-ct/text/moderate/general",
        );
      });
    });

    it("applies correct classes to contents", () => {
      render(<AccordionList data={mockData} type="single" />);

      const contents = screen.getAllByTestId("accordion-content");
      contents.forEach((content) => {
        expect(content).toHaveClass(
          "data-[state=closed]:animate-accordion-up",
          "data-[state=open]:animate-accordion-down",
          "overflow-hidden",
          "text-sm",
        );
      });

      // Check inner wrapper classes
      const contentWrappers = screen.getAllByText(/content/i);
      contentWrappers.forEach((wrapper) => {
        if (wrapper.textContent?.includes("content")) {
          expect(wrapper).toHaveClass("accordion-content", "px-5", "pb-5");
        }
      });
    });

    it("renders down icons with correct props", () => {
      render(<AccordionList data={mockData} type="single" />);

      const icons = screen.getAllByTestId("down-icon");
      expect(icons).toHaveLength(3);

      icons.forEach((icon) => {
        expect(icon).toHaveClass(
          "h-6",
          "w-6",
          "shrink-0",
          "transition-transform",
          "duration-300",
          "ease-[cubic-bezier(0.87,_0,_0.13,_1)]",
          "group-data-[state=open]:rotate-180",
          "[&>path]:fill-ct/icon/main/general",
        );
        expect(icon).toHaveAttribute("aria-hidden");
      });
    });
  });

  describe("Accordion Props Forwarding", () => {
    it("forwards accordion root props", () => {
      render(<AccordionList data={mockData} type="single" collapsible data-custom="test-prop" />);

      const root = screen.getByTestId("accordion-root");
      expect(root).toHaveAttribute("type", "single");
      expect(root).toHaveAttribute("collapsible", "");
      expect(root).toHaveAttribute("data-custom", "test-prop");
    });
  });

  describe("Accessibility", () => {
    it("sets aria-hidden on down icons", () => {
      render(<AccordionList data={mockData} type="single" />);

      const icons = screen.getAllByTestId("down-icon");
      icons.forEach((icon) => {
        expect(icon).toHaveAttribute("aria-hidden");
      });
    });

    it("renders proper button elements for triggers", () => {
      render(<AccordionList data={mockData} type="single" />);

      const triggers = screen.getAllByTestId("accordion-trigger");
      triggers.forEach((trigger) => {
        expect(trigger.tagName).toBe("BUTTON");
      });
    });
  });

  describe("Complex Data Scenarios", () => {
    it("handles mixed string and JSX content", () => {
      const mixedData = [
        { title: "String Title", content: "String Content" },
        {
          title: <h3 data-testid="complex-title">Complex Title</h3>,
          content: (
            <div data-testid="complex-content">
              <p>Paragraph 1</p>
              <p>Paragraph 2</p>
            </div>
          ),
        },
      ];

      render(<AccordionList data={mixedData} type="single" />);

      expect(screen.getByText("String Title")).toBeInTheDocument();
      expect(screen.getByText("String Content")).toBeInTheDocument();
      expect(screen.getByTestId("complex-title")).toBeInTheDocument();
      expect(screen.getByTestId("complex-content")).toBeInTheDocument();
      expect(screen.getByText("Paragraph 1")).toBeInTheDocument();
      expect(screen.getByText("Paragraph 2")).toBeInTheDocument();
    });

    it("handles special characters in content", () => {
      const specialData = [
        {
          title: "Special & Characters <Title>",
          content: "Content with 'quotes' and \"double quotes\" & symbols",
        },
      ];

      render(<AccordionList data={specialData} type="single" />);

      expect(screen.getByText("Special & Characters <Title>")).toBeInTheDocument();
      expect(screen.getByText(/Content with 'quotes'/)).toBeInTheDocument();
    });

    it("handles long content", () => {
      const longContent = "A".repeat(1000);
      const longData = [{ title: "Long Content", content: longContent }];

      render(<AccordionList data={longData} type="single" />);

      expect(screen.getByText("Long Content")).toBeInTheDocument();
      expect(screen.getByText(longContent)).toBeInTheDocument();
    });
  });

  describe("Edge Cases", () => {
    it("handles data with missing properties gracefully", () => {
      const incompleteData = [
        { title: "Only Title" } as any,
        { content: "Only Content" } as any,
        {} as any,
      ];

      render(<AccordionList data={incompleteData} type="single" />);

      expect(screen.getByText("Only Title")).toBeInTheDocument();
      expect(screen.getByText("Only Content")).toBeInTheDocument();

      const items = screen.getAllByTestId("accordion-item");
      expect(items).toHaveLength(3);
    });

    it("handles duplicate values", () => {
      const duplicateData = [
        { title: "Item 1", content: "Content 1", value: "duplicate" },
        { title: "Item 2", content: "Content 2", value: "duplicate" },
      ];

      render(<AccordionList data={duplicateData} type="single" />);

      const items = screen.getAllByTestId("accordion-item");
      expect(items).toHaveLength(2);
      expect(items[0]).toHaveAttribute("data-value", "duplicate");
      expect(items[1]).toHaveAttribute("data-value", "duplicate");
    });
  });
});
