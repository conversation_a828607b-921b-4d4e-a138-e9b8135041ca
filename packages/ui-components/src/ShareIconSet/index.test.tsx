import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ShareIconSet from "./index";

// Mock external dependencies
vi.mock("react-share", () => ({
  FacebookShareButton: ({ children, url, title, ...props }: any) => (
    <div data-testid="facebook-share" data-url={url} data-title={title} {...props}>
      {children}
    </div>
  ),
  LineShareButton: ({ children, url, title, ...props }: any) => (
    <div data-testid="line-share" data-url={url} data-title={title} {...props}>
      {children}
    </div>
  ),
}));

vi.mock("@pcsc/icons/feature", () => ({
  LinkIcon: ({ className }: any) => (
    <div data-testid="link-icon" className={className}>
      LinkIcon
    </div>
  ),
}));

vi.mock("@pcsc/icons/socialMedia", () => ({
  Facebook: ({ className }: any) => (
    <div data-testid="facebook-icon" className={className}>
      FacebookIcon
    </div>
  ),
  Line: ({ className }: any) => (
    <div data-testid="line-icon" className={className}>
      LineIcon
    </div>
  ),
}));

vi.mock("../Tooltip", () => ({
  Tooltip: ({ children, content, onOpenChange }: any) => (
    <div
      data-testid="tooltip"
      data-content={content}
      onMouseEnter={() => onOpenChange?.(true)}
      onMouseLeave={() => onOpenChange?.(false)}
    >
      {children}
    </div>
  ),
}));

vi.mock("@pcsc/utils", () => ({
  sendGTMClick: vi.fn(),
}));

// Mock window.location
const mockLocation = {
  href: "https://example.com/current-page",
  origin: "https://example.com",
};
Object.defineProperty(window, "location", {
  value: mockLocation,
  writable: true,
});

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(void 0),
  },
});

describe("ShareIconSet", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Basic Rendering", () => {
    it("renders all share buttons by default", () => {
      render(<ShareIconSet />);

      expect(screen.getByTestId("facebook-share")).toBeInTheDocument();
      expect(screen.getByTestId("line-share")).toBeInTheDocument();
      expect(screen.getByRole("button")).toBeInTheDocument(); // Link button
    });

    it("renders with correct default classes", () => {
      render(<ShareIconSet />);
      const container = screen.getByTestId("facebook-share").parentElement?.parentElement;
      expect(container).toHaveClass("flex", "gap-5");
    });

    it("renders with background classes when withBg is true", () => {
      render(<ShareIconSet withBg />);
      const container = screen.getByTestId("facebook-share").parentElement?.parentElement;
      expect(container).toHaveClass("flex", "gap-2", "p-3");
    });
  });

  describe("Icon Visibility", () => {
    it("hides Facebook icon when specified in hideIcon", () => {
      render(<ShareIconSet hideIcon={["facebook"]} />);

      expect(screen.queryByTestId("facebook-share")).not.toBeInTheDocument();
      expect(screen.getByTestId("line-share")).toBeInTheDocument();
      expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("hides Line icon when specified in hideIcon", () => {
      render(<ShareIconSet hideIcon={["line"]} />);

      expect(screen.getByTestId("facebook-share")).toBeInTheDocument();
      expect(screen.queryByTestId("line-share")).not.toBeInTheDocument();
      expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("hides Link button when specified in hideIcon", () => {
      render(<ShareIconSet hideIcon={["link"]} />);

      expect(screen.getByTestId("facebook-share")).toBeInTheDocument();
      expect(screen.getByTestId("line-share")).toBeInTheDocument();
      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("hides multiple icons when specified", () => {
      render(<ShareIconSet hideIcon={["facebook", "line"]} />);

      expect(screen.queryByTestId("facebook-share")).not.toBeInTheDocument();
      expect(screen.queryByTestId("line-share")).not.toBeInTheDocument();
      expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("shows no icons when all are hidden", () => {
      render(<ShareIconSet hideIcon={["facebook", "line", "link"]} />);

      expect(screen.queryByTestId("facebook-share")).not.toBeInTheDocument();
      expect(screen.queryByTestId("line-share")).not.toBeInTheDocument();
      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });
  });

  describe("Icon Sizes", () => {
    it("applies small size classes", () => {
      render(<ShareIconSet size="s" />);

      expect(screen.getByTestId("facebook-icon")).toHaveClass("h-4", "w-4");
      expect(screen.getByTestId("line-icon")).toHaveClass("h-4", "w-4");
      expect(screen.getByTestId("link-icon")).toHaveClass("h-4", "w-4");
    });

    it("applies medium size classes (default)", () => {
      render(<ShareIconSet size="m" />);

      expect(screen.getByTestId("facebook-icon")).toHaveClass("h-5", "w-5");
      expect(screen.getByTestId("line-icon")).toHaveClass("h-5", "w-5");
      expect(screen.getByTestId("link-icon")).toHaveClass("h-5", "w-5");
    });

    it("applies large size classes", () => {
      render(<ShareIconSet size="lg" />);

      expect(screen.getByTestId("facebook-icon")).toHaveClass("h-6", "w-6");
      expect(screen.getByTestId("line-icon")).toHaveClass("h-6", "w-6");
      expect(screen.getByTestId("link-icon")).toHaveClass("h-6", "w-6");
    });

    it("applies extra large size classes", () => {
      render(<ShareIconSet size="xl" />);

      expect(screen.getByTestId("facebook-icon")).toHaveClass("h-8", "w-8");
      expect(screen.getByTestId("line-icon")).toHaveClass("h-8", "w-8");
      expect(screen.getByTestId("link-icon")).toHaveClass("h-8", "w-8");
    });
  });

  describe("URL Path Handling", () => {
    it("uses current window location when no path provided", () => {
      render(<ShareIconSet />);

      expect(screen.getByTestId("facebook-share")).toHaveAttribute(
        "data-url",
        "https://example.com/current-page",
      );
      expect(screen.getByTestId("line-share")).toHaveAttribute(
        "data-url",
        "https://example.com/current-page",
      );
    });

    it("uses absolute URL when provided", () => {
      const absoluteUrl = "https://external.com/page";
      render(<ShareIconSet path={absoluteUrl} />);

      expect(screen.getByTestId("facebook-share")).toHaveAttribute("data-url", absoluteUrl);
      expect(screen.getByTestId("line-share")).toHaveAttribute("data-url", absoluteUrl);
    });

    it("converts relative path to absolute URL", () => {
      render(<ShareIconSet path="/relative-path" />);

      expect(screen.getByTestId("facebook-share")).toHaveAttribute(
        "data-url",
        "https://example.com/relative-path",
      );
      expect(screen.getByTestId("line-share")).toHaveAttribute(
        "data-url",
        "https://example.com/relative-path",
      );
    });
  });

  describe("Share Messages", () => {
    it("passes share message to share buttons", () => {
      const shareMessage = "Check out this awesome content!";
      render(<ShareIconSet shareMessage={shareMessage} />);

      expect(screen.getByTestId("facebook-share")).toHaveAttribute("data-title", shareMessage);
      expect(screen.getByTestId("line-share")).toHaveAttribute("data-title", shareMessage);
    });
  });

  describe("GTM Tracking", () => {
    it("calls sendGTMClick when Facebook button is clicked", async () => {
      const { sendGTMClick } = await import("@pcsc/utils");
      render(<ShareIconSet gtmValue="test-page" />);

      const facebookButton = screen.getByTestId("facebook-share").firstChild as HTMLElement;
      fireEvent.click(facebookButton);

      expect(sendGTMClick).toHaveBeenCalledWith("Facebook/test-page");
    });

    it("calls sendGTMClick when Line button is clicked", async () => {
      const { sendGTMClick } = await import("@pcsc/utils");
      render(<ShareIconSet gtmValue="test-page" />);

      const lineButton = screen.getByTestId("line-share").firstChild as HTMLElement;
      fireEvent.click(lineButton);

      expect(sendGTMClick).toHaveBeenCalledWith("Line/test-page");
    });

    it("calls sendGTMClick when Link button is clicked", async () => {
      const { sendGTMClick } = await import("@pcsc/utils");
      render(<ShareIconSet gtmValue="test-page" />);

      const linkButton = screen.getByRole("button");
      fireEvent.click(linkButton);

      expect(sendGTMClick).toHaveBeenCalledWith("Link/test-page");
    });

    it("does not call sendGTMClick when no gtmValue provided", async () => {
      const { sendGTMClick } = await import("@pcsc/utils");
      render(<ShareIconSet />);

      const linkButton = screen.getByRole("button");
      fireEvent.click(linkButton);

      expect(sendGTMClick).not.toHaveBeenCalled();
    });
  });

  describe("Clipboard Functionality", () => {
    it("copies URL to clipboard when link button is clicked", async () => {
      render(<ShareIconSet path="https://example.com/test" />);

      const linkButton = screen.getByRole("button");
      fireEvent.click(linkButton);

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith("https://example.com/test");
    });

    it("copies URL with share message when both provided", async () => {
      const shareMessage = "Check this out!";
      render(<ShareIconSet path="https://example.com/test" shareMessage={shareMessage} />);

      const linkButton = screen.getByRole("button");
      fireEvent.click(linkButton);

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
        "Check this out! https://example.com/test",
      );
    });
  });

  describe("Tooltip Behavior", () => {
    it("shows correct tooltip content for share buttons", () => {
      render(<ShareIconSet />);

      const tooltips = screen.getAllByTestId("tooltip");
      expect(tooltips[0]).toHaveAttribute("data-content", "分享至Facebook");
      expect(tooltips[1]).toHaveAttribute("data-content", "分享至Line");
      expect(tooltips[2]).toHaveAttribute("data-content", "分享鏈結");
    });

    it("updates link tooltip text after copying", async () => {
      render(<ShareIconSet />);

      const linkButton = screen.getByRole("button");
      const linkTooltip = linkButton.parentElement;

      // Initially shows "分享鏈結"
      expect(linkTooltip).toHaveAttribute("data-content", "分享鏈結");

      // Click to copy
      fireEvent.click(linkButton);

      // Should show "複製成功"
      await waitFor(() => {
        expect(linkTooltip).toHaveAttribute("data-content", "複製成功");
      });
    });

    it("resets link tooltip text when tooltip closes", async () => {
      render(<ShareIconSet />);

      const linkButton = screen.getByRole("button");
      const linkTooltip = linkButton.parentElement;

      // Click to copy and change text
      fireEvent.click(linkButton);
      await waitFor(() => {
        expect(linkTooltip).toHaveAttribute("data-content", "複製成功");
      });

      // Simulate tooltip close
      fireEvent.mouseLeave(linkTooltip!);

      expect(linkTooltip).toHaveAttribute("data-content", "分享鏈結");
    });
  });

  describe("Styling with Background", () => {
    it("applies background styles to buttons when withBg is true", () => {
      render(<ShareIconSet withBg />);

      const facebookButton = screen.getByTestId("facebook-share").firstChild as HTMLElement;
      const lineButton = screen.getByTestId("line-share").firstChild as HTMLElement;
      const linkButton = screen.getByRole("button");

      expect(facebookButton).toHaveClass(
        "rounded-xl",
        "bg-ct/button-share/main/default",
        "p-[6px]",
      );
      expect(lineButton).toHaveClass("rounded-xl", "bg-ct/button-share/main/default", "p-[6px]");
      expect(linkButton).toHaveClass("rounded-xl", "bg-ct/button-share/main/default", "p-[6px]");
    });

    it("applies hover effect styles when withBg is false", () => {
      render(<ShareIconSet />);

      const facebookButton = screen.getByTestId("facebook-share").firstChild as HTMLElement;
      const lineButton = screen.getByTestId("line-share").firstChild as HTMLElement;
      const linkButton = screen.getByRole("button");

      expect(facebookButton).toHaveClass("relative");
      expect(lineButton).toHaveClass("relative");
      expect(linkButton).toHaveClass("relative");
    });
  });

  describe("Accessibility", () => {
    it("has proper aria-labels for share buttons", () => {
      render(<ShareIconSet />);

      expect(screen.getByTestId("facebook-share")).toHaveAttribute(
        "aria-label",
        "facebook-share-button",
      );
      expect(screen.getByTestId("line-share")).toHaveAttribute("aria-label", "line-share-button");
    });

    it("link button has proper button type", () => {
      render(<ShareIconSet />);

      const linkButton = screen.getByRole("button");
      expect(linkButton).toHaveAttribute("type", "button");
    });
  });

  describe("Edge Cases", () => {
    it("handles empty hideIcon array", () => {
      render(<ShareIconSet hideIcon={[]} />);

      expect(screen.getByTestId("facebook-share")).toBeInTheDocument();
      expect(screen.getByTestId("line-share")).toBeInTheDocument();
      expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("handles undefined values gracefully", () => {
      render(<ShareIconSet path={undefined} shareMessage={undefined} gtmValue={undefined} />);

      expect(screen.getByTestId("facebook-share")).toBeInTheDocument();
      expect(screen.getByTestId("line-share")).toBeInTheDocument();
      expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("handles clipboard API failure gracefully", async () => {
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      vi.mocked(navigator.clipboard.writeText).mockRejectedValueOnce(new Error("Clipboard error"));

      render(<ShareIconSet />);

      const linkButton = screen.getByRole("button");
      fireEvent.click(linkButton);

      // Should not throw error
      await waitFor(() => {
        expect(navigator.clipboard.writeText).toHaveBeenCalled();
      });

      consoleSpy.mockRestore();
    });
  });
});
