import { render, screen, fireEvent } from "@testing-library/react";
import CardBasic from "./CardBasic";
import Card<PERSON>rame from "./CardFrame";

// Mock external dependencies
vi.mock("@pcsc/ui-components", () => ({
  Tag: ({ children, className, type, size }: any) => (
    <div data-testid="tag" className={className} data-type={type} data-size={size}>
      {children}
    </div>
  ),
  Image: ({ src, alt, className, objectFit, isForcedLoading, ...props }: any) => (
    <img
      data-testid="card-image"
      src={src || undefined}
      alt={alt}
      className={className}
      data-object-fit={objectFit}
      data-is-loading={isForcedLoading}
      {...props}
    />
  ),
  ShareIconSet: ({ path, withBg, gtmValue }: any) => (
    <div
      data-testid="share-icon-set"
      data-path={path || ""}
      data-with-bg={withBg}
      data-gtm-value={gtmValue}
    >
      ShareIconSet
    </div>
  ),
}));

describe("CardFrame Component", () => {
  describe("Basic Rendering", () => {
    it("renders with children", () => {
      render(
        <CardFrame>
          <div data-testid="child-content">Card content</div>
        </CardFrame>,
      );

      expect(screen.getByTestId("child-content")).toBeInTheDocument();
      expect(screen.getByText("Card content")).toBeInTheDocument();
    });

    it("applies default classes", () => {
      render(
        <CardFrame>
          <div data-testid="child">Content</div>
        </CardFrame>,
      );

      const frame = screen.getByTestId("child").parentElement;
      expect(frame).toHaveClass(
        "relative",
        "h-full",
        "w-full",
        "rounded-xl",
        "shadow-Card_light.Default",
        "hover:shadow-Card_light.Hover",
      );
    });

    it("applies custom className", () => {
      render(
        <CardFrame className="custom-frame-class">
          <div data-testid="child">Content</div>
        </CardFrame>,
      );

      const frame = screen.getByTestId("child").parentElement;
      expect(frame).toHaveClass("custom-frame-class");
    });

    it("forwards additional props", () => {
      render(
        <CardFrame data-custom="test-value" id="custom-id">
          <div data-testid="child">Content</div>
        </CardFrame>,
      );

      const frame = screen.getByTestId("child").parentElement;
      expect(frame).toHaveAttribute("data-custom", "test-value");
      expect(frame).toHaveAttribute("id", "custom-id");
    });
  });

  describe("Accessibility", () => {
    it("renders as a div element", () => {
      render(
        <CardFrame>
          <div data-testid="child">Content</div>
        </CardFrame>,
      );

      const frame = screen.getByTestId("child").parentElement;
      expect(frame?.tagName).toBe("DIV");
    });
  });
});

describe("CardBasic Component", () => {
  const defaultProps = {
    image: "https://example.com/image.jpg",
    children: <div data-testid="card-content">Card content</div>,
  };

  describe("Basic Rendering", () => {
    it("renders with required props", () => {
      render(<CardBasic {...defaultProps} />);

      expect(screen.getByTestId("card-content")).toBeInTheDocument();
      expect(screen.getByTestId("card-image")).toBeInTheDocument();
      expect(screen.getByTestId("card-image")).toHaveAttribute(
        "src",
        "https://example.com/image.jpg",
      );
    });

    it("renders children in content area", () => {
      render(
        <CardBasic {...defaultProps}>
          <h3 data-testid="card-title">Card Title</h3>
          <p data-testid="card-description">Card description</p>
        </CardBasic>,
      );

      expect(screen.getByTestId("card-title")).toBeInTheDocument();
      expect(screen.getByTestId("card-description")).toBeInTheDocument();
    });

    it("applies custom className to CardFrame", () => {
      render(<CardBasic {...defaultProps} className="custom-card-class" />);

      const cardFrame = screen.getByTestId("card-content").closest('[class*="custom-card-class"]');
      expect(cardFrame).toHaveClass("custom-card-class");
    });
  });

  describe("Image Handling", () => {
    it("renders image with correct props", () => {
      render(<CardBasic {...defaultProps} />);

      const image = screen.getByTestId("card-image");
      expect(image).toHaveAttribute("src", "https://example.com/image.jpg");
      expect(image).toHaveAttribute("alt", "card_image");
      expect(image).toHaveAttribute("data-object-fit", "cover");
    });

    it("passes imageProps to Image component", () => {
      const imageProps = {
        alt: "Custom alt text",
        "data-custom": "custom-value",
      };

      render(<CardBasic {...defaultProps} imageProps={imageProps} />);

      const image = screen.getByTestId("card-image");
      expect(image).toHaveAttribute("alt", "Custom alt text");
      expect(image).toHaveAttribute("data-custom", "custom-value");
    });

    it("applies loading state to image", () => {
      render(<CardBasic {...defaultProps} isLoading />);

      const image = screen.getByTestId("card-image");
      expect(image).toHaveAttribute("data-is-loading", "true");
    });

    it("applies correct CSS classes to image", () => {
      render(<CardBasic {...defaultProps} />);

      const image = screen.getByTestId("card-image");
      expect(image).toHaveClass(
        "image-mask",
        "aspect-video",
        "h-full",
        "min-w-full",
        "rounded-t-xl",
      );
    });
  });

  describe("Special Tag", () => {
    it("renders special tag when provided", () => {
      render(<CardBasic {...defaultProps} specialTag="Hot Deal" />);

      const tag = screen.getByTestId("tag");
      expect(tag).toBeInTheDocument();
      expect(tag).toHaveTextContent("Hot Deal");
    });

    it("applies correct props to special tag", () => {
      render(<CardBasic {...defaultProps} specialTag="Limited Time" />);

      const tag = screen.getByTestId("tag");
      expect(tag).toHaveAttribute("data-type", "moderate_general");
      expect(tag).toHaveAttribute("data-size", "M");
      expect(tag).toHaveClass(
        "absolute",
        "top-3",
        "z-10",
        "max-w-[150px]",
        "truncate",
        "rounded-l-none",
      );
    });

    it("does not render tag when specialTag is not provided", () => {
      render(<CardBasic {...defaultProps} />);

      expect(screen.queryByTestId("tag")).not.toBeInTheDocument();
    });

    it("does not render tag when specialTag is empty string", () => {
      render(<CardBasic {...defaultProps} specialTag="" />);

      expect(screen.queryByTestId("tag")).not.toBeInTheDocument();
    });
  });

  describe("Share Functionality", () => {
    it("renders ShareIconSet when share prop is provided", () => {
      const shareProps = {
        title: "Share this card",
        link: "https://example.com/share",
      };

      render(<CardBasic {...defaultProps} share={shareProps} />);

      const shareIconSet = screen.getByTestId("share-icon-set");
      expect(shareIconSet).toBeInTheDocument();
      expect(shareIconSet).toHaveAttribute("data-path", "https://example.com/share");
      expect(shareIconSet).toHaveAttribute("data-with-bg", "true");
      expect(shareIconSet).toHaveAttribute("data-gtm-value", "Share this card");
    });

    it("does not render ShareIconSet when share is null", () => {
      render(<CardBasic {...defaultProps} share={null} />);

      expect(screen.queryByTestId("share-icon-set")).not.toBeInTheDocument();
    });

    it("does not render ShareIconSet when share is undefined", () => {
      render(<CardBasic {...defaultProps} share={undefined} />);

      expect(screen.queryByTestId("share-icon-set")).not.toBeInTheDocument();
    });

    it("does not render ShareIconSet when share is falsy", () => {
      render(<CardBasic {...defaultProps} share={false as any} />);

      expect(screen.queryByTestId("share-icon-set")).not.toBeInTheDocument();
    });

    it("renders ShareIconSet with partial share data", () => {
      render(<CardBasic {...defaultProps} share={{ title: "Just title" }} />);

      const shareIconSet = screen.getByTestId("share-icon-set");
      expect(shareIconSet).toBeInTheDocument();
      expect(shareIconSet).toHaveAttribute("data-gtm-value", "Just title");
      expect(shareIconSet).toHaveAttribute("data-path", "");
    });

    it("applies correct styling to share container", () => {
      render(<CardBasic {...defaultProps} share={{ title: "Test" }} />);

      const shareContainer = screen.getByTestId("share-icon-set").parentElement;
      expect(shareContainer).toHaveClass(
        "absolute",
        "right-0",
        "top-0",
        "z-10",
        "opacity-0",
        "group-hover:opacity-100",
      );
    });
  });

  describe("Event Handling", () => {
    it("prevents event propagation when share container is clicked", () => {
      const shareProps = { title: "Share test" };
      render(<CardBasic {...defaultProps} share={shareProps} />);

      const shareContainer = screen.getByTestId("share-icon-set").parentElement!;

      const stopPropagationSpy = vi.fn();
      const preventDefaultSpy = vi.fn();

      // Create a proper event object
      const clickEvent = new MouseEvent("click", {
        bubbles: true,
        cancelable: true,
      });

      // Add spy methods to the event
      Object.defineProperty(clickEvent, "stopPropagation", {
        value: stopPropagationSpy,
        writable: true,
      });
      Object.defineProperty(clickEvent, "preventDefault", {
        value: preventDefaultSpy,
        writable: true,
      });

      fireEvent(shareContainer, clickEvent);

      expect(stopPropagationSpy).toHaveBeenCalled();
      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });

  describe("Layout and Structure", () => {
    it("applies correct layout classes", () => {
      render(<CardBasic {...defaultProps} />);

      // Check main container
      const cardFrame = screen.getByTestId("card-content").closest('[class*="flex-col"]');
      expect(cardFrame).toHaveClass("flex", "flex-col");

      // Check image container
      const imageContainer = screen.getByTestId("card-image").parentElement;
      expect(imageContainer).toHaveClass("group", "relative", "aspect-video", "overflow-hidden");

      // Check content container
      const contentContainer = screen.getByTestId("card-content").parentElement;
      expect(contentContainer).toHaveClass(
        "relative",
        "flex",
        "flex-grow",
        "rounded-b-xl",
        "bg-ct/card-basic/main/container",
        "px-5",
        "pb-4",
        "pt-3",
      );
    });

    it("maintains proper stacking order with z-index classes", () => {
      render(<CardBasic {...defaultProps} specialTag="Test Tag" share={{ title: "Share" }} />);

      const tag = screen.getByTestId("tag");
      const shareContainer = screen.getByTestId("share-icon-set").parentElement;

      expect(tag).toHaveClass("z-10");
      expect(shareContainer).toHaveClass("z-10");
    });
  });

  describe("Hover Effects", () => {
    it("applies hover effect classes to image mask", () => {
      render(<CardBasic {...defaultProps} />);

      const image = screen.getByTestId("card-image");
      expect(image).toHaveClass("hover:after:bg-ct/card-basic/main/dim");
    });

    it("applies group hover effect to share container", () => {
      render(<CardBasic {...defaultProps} share={{ title: "Test" }} />);

      const shareContainer = screen.getByTestId("share-icon-set").parentElement;
      expect(shareContainer).toHaveClass("group-hover:opacity-100");
    });
  });

  describe("Integration with CardFrame", () => {
    it("uses CardFrame as the root container", () => {
      render(<CardBasic {...defaultProps} />);

      const cardContent = screen.getByTestId("card-content");
      const rootContainer = cardContent.closest('[class*="shadow-Card_light"]');

      expect(rootContainer).toBeInTheDocument();
      expect(rootContainer).toHaveClass("shadow-Card_light.Default");
    });

    it("passes className to CardFrame", () => {
      render(<CardBasic {...defaultProps} className="test-class" />);

      const cardContent = screen.getByTestId("card-content");
      const rootContainer = cardContent.closest('[class*="test-class"]');

      expect(rootContainer).toHaveClass("test-class");
    });
  });

  describe("Edge Cases", () => {
    it("handles empty image URL", () => {
      render(<CardBasic {...defaultProps} image="" />);

      const image = screen.getByTestId("card-image");
      expect(image).not.toHaveAttribute("src");
    });

    it("handles complex children structure", () => {
      render(
        <CardBasic {...defaultProps}>
          <div data-testid="complex-child">
            <h1>Title</h1>
            <div>
              <p>Description</p>
              <button>Action</button>
            </div>
          </div>
        </CardBasic>,
      );

      expect(screen.getByTestId("complex-child")).toBeInTheDocument();
      expect(screen.getByText("Title")).toBeInTheDocument();
      expect(screen.getByText("Description")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Action" })).toBeInTheDocument();
    });

    it("handles multiple imageProps", () => {
      const complexImageProps = {
        alt: "Complex image",
        loading: "lazy" as const,
        "data-test": "image-test",
        style: { border: "1px solid red" },
      };

      render(<CardBasic {...defaultProps} imageProps={complexImageProps} />);

      const image = screen.getByTestId("card-image");
      expect(image).toHaveAttribute("alt", "Complex image");
      expect(image).toHaveAttribute("loading", "lazy");
      expect(image).toHaveAttribute("data-test", "image-test");
    });
  });
});
