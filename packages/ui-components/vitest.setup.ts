import "@testing-library/jest-dom/vitest";

// Mock ResizeObserver
class ResizeObserverMock {
  observe() {}
  unobserve() {}
  disconnect() {}
}

Object.defineProperty(window, "ResizeObserver", {
  writable: true,
  configurable: true,
  value: ResizeObserverMock,
});

// Mock @pcsc/tailwind-config/utils to avoid ES module issues
vi.mock("@pcsc/tailwind-config/utils", () => ({
  cn: (...inputs: unknown[]) => {
    // Simple mock that just joins class names
    return inputs.filter(Boolean).join(" ");
  },
}));
