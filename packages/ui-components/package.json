{"name": "@pcsc/ui-components", "version": "0.0.1-rc.1", "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "type": "module", "exports": {".": "./src/index.ts"}, "scripts": {"lint": "eslint . --quiet", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit", "build": "vite build", "login:artifact": "aws codeartifact login --tool npm --repository frontend --domain uniopen --domain-owner 471112919450 --region ap-northeast-1 && echo \"registry=https://registry.npmjs.org/\" >> ~/.npmrc && npm config set \"@pcsc:registry\" https://uniopen-471112919450.d.codeartifact.ap-northeast-1.amazonaws.com/npm/frontend/", "publish_package:rc": "pnpm run login:artifact && mkdir ./.git && pnpm version prerelease --preid=rc -m 'Bump pre-release version - %s  [skip ci]' && pnpm pack && pnpm publish --tag rc && rm -rf ./.git", "storybook": "storybook dev -p 6006", "build:storybook": "storybook build", "test": "TZ=Asia/Taipei vitest --run --coverage", "test:watch": "TZ=Asia/Taipei vitest", "test:report": "TZ=Asia/Taipei vitest --ui"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.2", "@pcsc/eslint-config": "workspace:*", "@pcsc/tailwind-config": "workspace:*", "@pcsc/typescript-config": "workspace:*", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/nextjs": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/test": "^8.6.14", "@svgr/webpack": "8.1.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.5.2", "@turbo/gen": "^1.12.4", "@types/node": "^20.11.24", "@types/react": "19.1.0", "@types/react-dom": "19.1.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.18", "jsdom": "^26.1.0", "postcss": "^8.5.5", "postcss-loader": "^8.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "storybook": "^8.6.14", "tailwindcss": "^3.4.17", "typescript": "5.5.4", "vite": "^6.3.4", "vite-plugin-dts": "^4.4.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "dependencies": {"@pcsc/icons": "workspace:*", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.1.2", "antd-watermark": "^1.0.1", "class-variance-authority": "^0.7.1", "date-fns": "^3.3.1", "date-fns-tz": "^3.1.3", "embla-carousel": "^8.5.2", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.5.2", "ics": "^3.8.1", "react-day-picker": "9.6.4", "react-hot-toast": "^2.5.2", "react-share": "5.2.2", "usehooks-ts": "^3.1.1", "vaul": "^1.1.1", "yet-another-react-lightbox": "^3.21.8"}}